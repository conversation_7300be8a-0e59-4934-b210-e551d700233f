#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OBS VST滤镜管理器
功能：连接OBS、获取媒体源、管理滤镜、添加VST滤镜
"""

import tkinter as tk
from tkinter import ttk, messagebox
import websocket
import json
import time
import threading
import os
import base64
import struct

class OBSVSTManager:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("OBS VST滤镜管理器")
        self.root.geometry("800x600")
        
        # WebSocket连接
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0
        
        # 数据存储
        self.media_sources = []
        self.current_filters = []
        self.selected_source = ""
        
        # VST插件配置 - 根据你的实际路径更新
        self.vst_plugins = {
            "Auburn Sounds Graillon 3-64": {
                "display_name": "Graillon 音调变声器",
                "filter_kind": "vst_filter",
                "plugin_path": r"C:\Program Files\VSTPlugins\Auburn Sounds Graillon 3-64.dll"
            },
            "TSE_808_2.0_x64": {
                "display_name": "TSE808 失真效果器",
                "filter_kind": "vst_filter",
                "plugin_path": r"C:\Program Files\VSTPlugins\TSE_808_2.0_x64.dll"
            },
            "TAL-Reverb-4-64": {
                "display_name": "TAL 混响效果器",
                "filter_kind": "vst_filter",
                "plugin_path": r"C:\Program Files\VSTPlugins\TAL-Reverb-4-64.dll"
            }
        }
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 连接区域
        conn_frame = ttk.LabelFrame(main_frame, text="OBS连接", padding="5")
        conn_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(conn_frame, text="WebSocket地址:").grid(row=0, column=0, sticky=tk.W)
        self.ws_url_var = tk.StringVar(value="ws://localhost:4455")
        ttk.Entry(conn_frame, textvariable=self.ws_url_var, width=30).grid(row=0, column=1, padx=(5, 10))
        
        self.connect_btn = ttk.Button(conn_frame, text="连接OBS", command=self.connect_obs)
        self.connect_btn.grid(row=0, column=2)
        
        self.status_label = ttk.Label(conn_frame, text="未连接", foreground="red")
        self.status_label.grid(row=0, column=3, padx=(10, 0))
        
        # 媒体源区域
        source_frame = ttk.LabelFrame(main_frame, text="媒体源管理", padding="5")
        source_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(source_frame, text="选择媒体源:").grid(row=0, column=0, sticky=tk.W)
        self.source_combo = ttk.Combobox(source_frame, state="readonly", width=40)
        self.source_combo.grid(row=0, column=1, padx=(5, 10))
        self.source_combo.bind('<<ComboboxSelected>>', self.on_source_selected)
        
        ttk.Button(source_frame, text="刷新媒体源", command=self.refresh_media_sources).grid(row=0, column=2)
        
        # 滤镜管理区域
        filter_frame = ttk.LabelFrame(main_frame, text="滤镜管理", padding="5")
        filter_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 滤镜列表
        columns = ('name', 'type', 'enabled')
        self.filter_tree = ttk.Treeview(filter_frame, columns=columns, show='headings', height=8)
        self.filter_tree.heading('name', text='滤镜名称')
        self.filter_tree.heading('type', text='滤镜类型')
        self.filter_tree.heading('enabled', text='状态')
        
        self.filter_tree.column('name', width=200)
        self.filter_tree.column('type', width=150)
        self.filter_tree.column('enabled', width=80)
        
        self.filter_tree.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 滚动条
        scrollbar = ttk.Scrollbar(filter_frame, orient=tk.VERTICAL, command=self.filter_tree.yview)
        scrollbar.grid(row=0, column=3, sticky=(tk.N, tk.S))
        self.filter_tree.configure(yscrollcommand=scrollbar.set)
        
        filter_btn_frame = ttk.Frame(filter_frame)
        filter_btn_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(5, 0))

        ttk.Button(filter_btn_frame, text="刷新滤镜", command=self.refresh_filters).grid(row=0, column=0, sticky=tk.W)
        ttk.Button(filter_btn_frame, text="调节参数", command=self.open_parameter_window).grid(row=0, column=1, padx=(10, 0), sticky=tk.W)
        ttk.Button(filter_btn_frame, text="分析Chunk", command=self.analyze_chunk_data).grid(row=0, column=2, padx=(10, 0), sticky=tk.W)
        ttk.Button(filter_btn_frame, text="删除滤镜", command=self.remove_selected_filter).grid(row=0, column=3, padx=(10, 0), sticky=tk.W)
        
        # VST插件添加区域
        vst_frame = ttk.LabelFrame(main_frame, text="添加VST滤镜", padding="5")
        vst_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(vst_frame, text="选择VST插件:").grid(row=0, column=0, sticky=tk.W)
        self.vst_combo = ttk.Combobox(vst_frame, values=list(self.vst_plugins.keys()), 
                                     state="readonly", width=30)
        self.vst_combo.grid(row=0, column=1, padx=(5, 10))
        
        ttk.Label(vst_frame, text="滤镜名称:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.filter_name_var = tk.StringVar(value="VST滤镜")
        ttk.Entry(vst_frame, textvariable=self.filter_name_var, width=20).grid(row=0, column=3, padx=(5, 10))
        
        ttk.Button(vst_frame, text="添加VST滤镜", command=self.add_vst_filter).grid(row=0, column=4)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="5")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        self.log_text = tk.Text(log_frame, height=8, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        main_frame.rowconfigure(4, weight=1)
        filter_frame.columnconfigure(0, weight=1)
        filter_frame.rowconfigure(0, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
    def log(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        print(message)  # 同时输出到控制台
        
    def connect_obs(self):
        """连接到OBS"""
        if self.is_connected:
            self.disconnect_obs()
            return
            
        self.log("🔄 正在连接到OBS...")
        self.connect_btn.config(state="disabled")
        
        def connect_thread():
            try:
                obs_ws_url = self.ws_url_var.get()
                self.ws = websocket.create_connection(obs_ws_url, timeout=5)
                self.log("✅ WebSocket连接已建立，等待Hello消息...")
                
                # 接收Hello消息 (Opcode 0)
                hello_raw = self.ws.recv()
                hello_data = json.loads(hello_raw)
                self.log(f"📨 收到Hello消息: OBS WebSocket v{hello_data.get('d', {}).get('obsWebSocketVersion', 'Unknown')}")
                
                if hello_data.get("op") != 0:
                    raise ValueError("收到的第一个消息不是Hello (Opcode 0)")
                
                rpc_version = hello_data.get("d", {}).get("rpcVersion", 1)
                authentication_required = hello_data.get("d", {}).get("authentication") is not None
                
                if authentication_required:
                    raise ConnectionAbortedError("OBS需要身份验证，请在OBS中禁用身份验证")
                
                # 发送Identify消息 (Opcode 1)
                identify_payload = {
                    "op": 1,
                    "d": {
                        "rpcVersion": rpc_version,
                        "eventSubscriptions": 0  # 不订阅事件
                    }
                }
                self.ws.send(json.dumps(identify_payload))
                self.log("📤 已发送Identify消息...")
                
                # 接收Identified消息 (Opcode 2)
                identified_raw = self.ws.recv()
                identified_data = json.loads(identified_raw)
                self.log(f"📨 收到Identified消息: {identified_data}")
                
                if identified_data.get("op") != 2:
                    raise ValueError("收到的第二个消息不是Identified (Opcode 2)")
                
                # 连接成功
                self.root.after(0, self.on_connect_success)
                
            except Exception as e:
                self.root.after(0, self.on_connect_error, str(e))
        
        threading.Thread(target=connect_thread, daemon=True).start()
        
    def on_connect_success(self):
        """连接成功回调"""
        self.is_connected = True
        self.status_label.config(text="已连接", foreground="green")
        self.connect_btn.config(text="断开连接", state="normal")
        self.log("🎉 OBS连接成功！")
        
        # 自动刷新媒体源
        self.refresh_media_sources()
        
    def on_connect_error(self, error_msg):
        """连接失败回调"""
        self.is_connected = False
        self.status_label.config(text="连接失败", foreground="red")
        self.connect_btn.config(text="连接OBS", state="normal")
        self.log(f"❌ 连接失败: {error_msg}")
        
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
    def disconnect_obs(self):
        """断开OBS连接"""
        self.is_connected = False
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
        self.status_label.config(text="未连接", foreground="red")
        self.connect_btn.config(text="连接OBS")
        self.log("👋 已断开OBS连接")
        
    def send_request(self, request_type, request_data=None, timeout=10):
        """发送请求并获取响应"""
        if not self.is_connected or not self.ws:
            return None
            
        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"
        
        payload = {
            "op": 6,  # Request
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            self.ws.send(json.dumps(payload))
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)
                    
                    if (response_data.get("op") == 7 and 
                        response_data.get("d", {}).get("requestId") == request_id):
                        return response_data.get("d", {})
                        
                except websocket.WebSocketTimeoutException:
                    continue
                except:
                    continue
                    
            return None
            
        except Exception as e:
            self.log(f"❌ 发送请求失败: {e}")
            return None

    def refresh_media_sources(self):
        """刷新媒体源列表"""
        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接到OBS")
            return

        self.log("🔍 正在获取媒体源列表...")

        def refresh_thread():
            response_data = self.send_request("GetInputList")
            if not response_data:
                self.root.after(0, lambda: self.log("❌ 获取媒体源失败"))
                return

            request_status = response_data.get("requestStatus", {})
            if not request_status.get("result"):
                error_msg = request_status.get("comment", "未知错误")
                self.root.after(0, lambda: self.log(f"❌ 获取媒体源失败: {error_msg}"))
                return

            inputs_data = response_data.get("responseData", {})
            inputs_list = inputs_data.get("inputs", [])

            # 处理媒体源数据
            self.root.after(0, lambda: self.process_media_sources(inputs_list))

        threading.Thread(target=refresh_thread, daemon=True).start()

    def process_media_sources(self, inputs_list):
        """处理媒体源数据"""
        self.media_sources = []
        source_names = []

        self.log(f"✅ 成功获取到 {len(inputs_list)} 个输入源")

        for input_item in inputs_list:
            input_name = input_item.get('inputName', '')
            input_kind = input_item.get('inputKind', '')

            # 存储媒体源信息
            source_info = {
                'name': input_name,
                'kind': input_kind,
                'unversioned_kind': input_item.get('unversionedInputKind', ''),
            }
            self.media_sources.append(source_info)
            source_names.append(input_name)

            # 分类显示
            if input_kind in ['ffmpeg_source', 'vlc_source']:
                self.log(f"  🎬 媒体源: {input_name} ({input_kind})")
            elif input_kind in ['wasapi_input_capture', 'wasapi_output_capture', 'pulse_input_capture']:
                self.log(f"  🎵 音频源: {input_name} ({input_kind})")
            elif input_kind in ['dshow_input', 'v4l2_input']:
                self.log(f"  📹 视频源: {input_name} ({input_kind})")
            else:
                self.log(f"  ⚪ 其他源: {input_name} ({input_kind})")

        # 更新下拉框
        self.source_combo['values'] = source_names
        if source_names:
            self.source_combo.set(source_names[0])
            self.selected_source = source_names[0]
            self.refresh_filters()

        self.log(f"📊 共找到 {len(source_names)} 个媒体源")

    def on_source_selected(self, event):
        """媒体源选择事件"""
        self.selected_source = self.source_combo.get()
        self.log(f"📌 选择媒体源: {self.selected_source}")
        self.refresh_filters()

    def refresh_filters(self):
        """刷新滤镜列表"""
        if not self.selected_source:
            return

        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接到OBS")
            return

        self.log(f"🔍 正在获取媒体源 '{self.selected_source}' 的滤镜...")

        def refresh_thread():
            response_data = self.send_request("GetSourceFilterList", {"sourceName": self.selected_source})
            if not response_data:
                self.root.after(0, lambda: self.log("❌ 获取滤镜失败"))
                return

            request_status = response_data.get("requestStatus", {})
            if not request_status.get("result"):
                error_msg = request_status.get("comment", "未知错误")
                self.root.after(0, lambda: self.log(f"❌ 获取滤镜失败: {error_msg}"))
                return

            filters_data = response_data.get("responseData", {})
            filters_list = filters_data.get("filters", [])

            # 处理滤镜数据
            self.root.after(0, lambda: self.process_filters(filters_list))

        threading.Thread(target=refresh_thread, daemon=True).start()

    def process_filters(self, filters_list):
        """处理滤镜数据"""
        # 清空树形控件
        for item in self.filter_tree.get_children():
            self.filter_tree.delete(item)

        self.current_filters = []
        vst_count = 0

        self.log(f"✅ 找到 {len(filters_list)} 个滤镜")

        for filter_item in filters_list:
            filter_name = filter_item.get('filterName', '')
            filter_kind = filter_item.get('filterKind', '')
            filter_enabled = filter_item.get('filterEnabled', False)

            # 存储滤镜数据
            filter_info = {
                'name': filter_name,
                'kind': filter_kind,
                'enabled': filter_enabled,
                'settings': filter_item.get('filterSettings', {})
            }
            self.current_filters.append(filter_info)

            # 检查是否为VST滤镜
            is_vst = 'vst' in filter_kind.lower()
            if is_vst:
                vst_count += 1

            # 添加到树形控件
            enabled_text = "✅ 启用" if filter_enabled else "❌ 禁用"
            item_id = self.filter_tree.insert('', tk.END, values=(filter_name, filter_kind, enabled_text))

            # 显示滤镜信息（移除无效的列设置）
            if is_vst:
                self.log(f"  🎛️ VST滤镜: {filter_name} ({filter_kind})")
            else:
                self.log(f"  ⚪ 其他滤镜: {filter_name} ({filter_kind})")

        if vst_count > 0:
            self.log(f"🎛️ 共找到 {vst_count} 个VST滤镜")
        else:
            self.log("📭 未找到VST滤镜")

    def add_vst_filter(self):
        """添加VST滤镜"""
        if not self.selected_source:
            messagebox.showwarning("警告", "请先选择媒体源")
            return

        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接到OBS")
            return

        plugin_name = self.vst_combo.get()
        filter_name = self.filter_name_var.get().strip()

        if not plugin_name:
            messagebox.showwarning("警告", "请选择VST插件")
            return

        if not filter_name:
            messagebox.showwarning("警告", "请输入滤镜名称")
            return

        plugin_config = self.vst_plugins[plugin_name]
        plugin_path = plugin_config["plugin_path"]

        # 检查插件文件是否存在
        if not os.path.exists(plugin_path):
            messagebox.showerror("错误", f"VST插件文件不存在:\n{plugin_path}")
            return

        self.log(f"🔧 正在添加VST滤镜: {filter_name} ({plugin_config['display_name']})")

        def add_thread():
            try:
                # 检查滤镜是否已存在
                existing_response = self.send_request("GetSourceFilter", {
                    "sourceName": self.selected_source,
                    "filterName": filter_name
                })

                if existing_response and existing_response.get("requestStatus", {}).get("result"):
                    self.root.after(0, lambda: self.log(f"⚠️ 滤镜 '{filter_name}' 已存在，将先删除"))

                    delete_response = self.send_request("RemoveSourceFilter", {
                        "sourceName": self.selected_source,
                        "filterName": filter_name
                    })
                    time.sleep(0.5)

                # 创建VST滤镜
                filter_settings = {
                    "plugin_path": plugin_path
                }

                response = self.send_request("CreateSourceFilter", {
                    "sourceName": self.selected_source,
                    "filterName": filter_name,
                    "filterKind": plugin_config["filter_kind"],
                    "filterSettings": filter_settings
                })

                if response and response.get("requestStatus", {}).get("result"):
                    self.root.after(0, lambda: [
                        self.log(f"✅ VST滤镜 '{filter_name}' 添加成功"),
                        self.refresh_filters()
                    ])
                else:
                    error_msg = response.get("requestStatus", {}).get("comment", "未知错误") if response else "无响应"
                    self.root.after(0, lambda: self.log(f"❌ 创建VST滤镜失败: {error_msg}"))

            except Exception as e:
                self.root.after(0, lambda: self.log(f"❌ 添加VST滤镜失败: {e}"))

        threading.Thread(target=add_thread, daemon=True).start()

    def get_selected_filter(self):
        """获取选中的滤镜"""
        selection = self.filter_tree.selection()
        if not selection:
            return None

        item = selection[0]
        filter_name = self.filter_tree.item(item)['values'][0]

        # 从当前滤镜列表中找到对应的滤镜信息
        for filter_info in self.current_filters:
            if filter_info['name'] == filter_name:
                return filter_info
        return None

    def remove_selected_filter(self):
        """删除选中的滤镜"""
        filter_info = self.get_selected_filter()
        if not filter_info:
            messagebox.showwarning("警告", "请先选择要删除的滤镜")
            return

        if not self.selected_source:
            messagebox.showwarning("警告", "请先选择媒体源")
            return

        filter_name = filter_info['name']

        # 确认删除
        if not messagebox.askyesno("确认删除", f"确定要删除滤镜 '{filter_name}' 吗？"):
            return

        self.log(f"🗑️ 正在删除滤镜: {filter_name}")

        def remove_thread():
            try:
                response = self.send_request("RemoveSourceFilter", {
                    "sourceName": self.selected_source,
                    "filterName": filter_name
                })

                if response and response.get("requestStatus", {}).get("result"):
                    self.root.after(0, lambda: [
                        self.log(f"✅ 滤镜 '{filter_name}' 删除成功"),
                        self.refresh_filters()
                    ])
                else:
                    error_msg = response.get("requestStatus", {}).get("comment", "未知错误") if response else "无响应"
                    self.root.after(0, lambda: self.log(f"❌ 删除滤镜失败: {error_msg}"))

            except Exception as e:
                self.root.after(0, lambda: self.log(f"❌ 删除滤镜失败: {e}"))

        threading.Thread(target=remove_thread, daemon=True).start()

    def open_parameter_window(self):
        """打开参数调节窗口"""
        filter_info = self.get_selected_filter()
        if not filter_info:
            messagebox.showwarning("警告", "请先选择要调节的滤镜")
            return

        if not self.selected_source:
            messagebox.showwarning("警告", "请先选择媒体源")
            return

        # 检查是否为VST滤镜
        if 'vst' not in filter_info['kind'].lower():
            messagebox.showinfo("提示", "只能调节VST滤镜的参数")
            return

        # 创建参数调节窗口
        param_window = VSTParameterWindow(self, self.selected_source, filter_info)

    def set_filter_parameter(self, source_name, filter_name, param_name, value):
        """设置滤镜参数"""
        try:
            response = self.send_request("SetSourceFilterSettings", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterSettings": {param_name: value}
            })

            if response and response.get("requestStatus", {}).get("result"):
                self.log(f"✅ 参数设置成功: {param_name} = {value}")
                return True
            else:
                error_msg = response.get("requestStatus", {}).get("comment", "未知错误") if response else "无响应"
                self.log(f"❌ 参数设置失败: {error_msg}")
                return False

        except Exception as e:
            self.log(f"❌ 参数设置失败: {e}")
            return False

    def get_filter_settings(self, source_name, filter_name):
        """获取滤镜设置"""
        try:
            response = self.send_request("GetSourceFilter", {
                "sourceName": source_name,
                "filterName": filter_name
            })

            if response and response.get("requestStatus", {}).get("result"):
                return response.get("responseData", {}).get("filterSettings", {})
            else:
                return {}

        except Exception as e:
            self.log(f"❌ 获取滤镜设置失败: {e}")
            return {}

    def analyze_chunk_data(self):
        """分析VST插件的chunk数据"""
        filter_info = self.get_selected_filter()
        if not filter_info:
            messagebox.showwarning("警告", "请先选择要分析的滤镜")
            return

        if not self.selected_source:
            messagebox.showwarning("警告", "请先选择媒体源")
            return

        # 检查是否为VST滤镜
        if 'vst' not in filter_info['kind'].lower():
            messagebox.showinfo("提示", "只能分析VST滤镜的chunk数据")
            return

        # 创建chunk分析窗口
        chunk_window = ChunkAnalyzerWindow(self, self.selected_source, filter_info)

    def run(self):
        """运行程序"""
        self.log("🎛️ OBS VST滤镜管理器已启动")
        self.log("请先连接到OBS，然后选择媒体源")

        try:
            self.root.mainloop()
        finally:
            if self.is_connected:
                self.disconnect_obs()


class VSTParameterWindow:
    """VST参数调节窗口"""

    def __init__(self, parent, source_name, filter_info):
        self.parent = parent
        self.source_name = source_name
        self.filter_info = filter_info
        self.filter_name = filter_info['name']

        # 创建窗口
        self.window = tk.Toplevel(parent.root)
        self.window.title(f"VST参数调节 - {self.filter_name}")
        self.window.geometry("500x400")
        self.window.resizable(True, True)

        # 参数控件存储
        self.param_vars = {}
        self.param_widgets = {}

        self.setup_ui()
        self.load_current_settings()

    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 滤镜信息
        info_frame = ttk.LabelFrame(main_frame, text="滤镜信息", padding="5")
        info_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(info_frame, text=f"媒体源: {self.source_name}").grid(row=0, column=0, sticky=tk.W)
        ttk.Label(info_frame, text=f"滤镜名称: {self.filter_name}").grid(row=1, column=0, sticky=tk.W)
        ttk.Label(info_frame, text=f"滤镜类型: {self.filter_info['kind']}").grid(row=2, column=0, sticky=tk.W)

        # 参数调节区域
        param_frame = ttk.LabelFrame(main_frame, text="参数调节", padding="5")
        param_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 创建滚动区域
        canvas = tk.Canvas(param_frame, height=200)
        scrollbar = ttk.Scrollbar(param_frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 按钮区域
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=2, column=0, sticky=(tk.W, tk.E))

        ttk.Button(btn_frame, text="刷新参数", command=self.load_current_settings).grid(row=0, column=0, sticky=tk.W)
        ttk.Button(btn_frame, text="重置默认", command=self.reset_to_defaults).grid(row=0, column=1, padx=(10, 0), sticky=tk.W)
        ttk.Button(btn_frame, text="关闭", command=self.window.destroy).grid(row=0, column=2, padx=(10, 0), sticky=tk.E)

        # 配置网格权重
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        param_frame.columnconfigure(0, weight=1)
        param_frame.rowconfigure(0, weight=1)
        btn_frame.columnconfigure(2, weight=1)

    def load_current_settings(self):
        """加载当前参数设置"""
        def load_thread():
            settings = self.parent.get_filter_settings(self.source_name, self.filter_name)
            self.window.after(0, lambda: self.display_parameters(settings))

        threading.Thread(target=load_thread, daemon=True).start()

    def display_parameters(self, settings):
        """显示参数控件"""
        # 清空现有控件
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        self.param_vars.clear()
        self.param_widgets.clear()

        if not settings:
            ttk.Label(self.scrollable_frame, text="无法获取参数信息").grid(row=0, column=0, pady=10)
            return

        # 根据滤镜类型创建预设参数
        preset_params = self.get_preset_parameters()

        row = 0

        # 显示预设参数
        if preset_params:
            ttk.Label(self.scrollable_frame, text="常用参数:", font=('Arial', 10, 'bold')).grid(row=row, column=0, columnspan=3, sticky=tk.W, pady=(0, 5))
            row += 1

            for param_name, param_info in preset_params.items():
                current_value = settings.get(param_name, param_info.get('default', 0))
                self.create_parameter_widget(row, param_name, current_value, param_info)
                row += 1

        # 显示其他参数
        other_params = {k: v for k, v in settings.items() if k not in preset_params}
        if other_params:
            ttk.Separator(self.scrollable_frame, orient='horizontal').grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
            row += 1

            ttk.Label(self.scrollable_frame, text="其他参数:", font=('Arial', 10, 'bold')).grid(row=row, column=0, columnspan=3, sticky=tk.W, pady=(0, 5))
            row += 1

            for param_name, value in other_params.items():
                param_info = {'min': -100, 'max': 100, 'default': 0, 'description': param_name}
                self.create_parameter_widget(row, param_name, value, param_info)
                row += 1

    def get_preset_parameters(self):
        """获取预设参数配置"""
        filter_kind = self.filter_info['kind'].lower()

        if 'graillon' in self.filter_name.lower() or 'pitch' in filter_kind:
            return {
                'pitch': {'min': -12, 'max': 12, 'default': 0, 'description': '音调偏移 (半音)'},
                'formant': {'min': 50, 'max': 150, 'default': 100, 'description': '共振峰调节 (%)'},
                'mix': {'min': 0, 'max': 100, 'default': 100, 'description': '干湿混合 (%)'},
            }
        elif 'tse' in self.filter_name.lower() or '808' in self.filter_name.lower():
            return {
                'drive': {'min': 0, 'max': 100, 'default': 30, 'description': '失真强度 (%)'},
                'tone': {'min': 0, 'max': 100, 'default': 50, 'description': '音色控制 (%)'},
                'level': {'min': 0, 'max': 100, 'default': 80, 'description': '输出电平 (%)'},
            }
        elif 'tal' in self.filter_name.lower() or 'reverb' in self.filter_name.lower():
            return {
                'roomsize': {'min': 0, 'max': 100, 'default': 40, 'description': '房间大小 (%)'},
                'damping': {'min': 0, 'max': 100, 'default': 60, 'description': '阻尼控制 (%)'},
                'mix': {'min': 0, 'max': 100, 'default': 25, 'description': '混响混合 (%)'},
            }
        else:
            return {}

    def create_parameter_widget(self, row, param_name, current_value, param_info):
        """创建参数控件"""
        # 参数名称和描述
        desc = param_info.get('description', param_name)
        ttk.Label(self.scrollable_frame, text=f"{desc}:").grid(row=row, column=0, sticky=tk.W, padx=(0, 10))

        # 判断参数类型
        if isinstance(current_value, bool):
            # 布尔参数 - 使用复选框
            var = tk.BooleanVar(value=current_value)
            widget = ttk.Checkbutton(self.scrollable_frame, variable=var,
                                   command=lambda: self.on_parameter_change(param_name, var.get()))
            widget.grid(row=row, column=1, sticky=tk.W)

        elif isinstance(current_value, (int, float)):
            # 数值参数 - 使用滑块和输入框
            var = tk.DoubleVar(value=float(current_value))

            # 滑块
            min_val = param_info.get('min', -100)
            max_val = param_info.get('max', 100)

            slider = ttk.Scale(self.scrollable_frame, from_=min_val, to=max_val,
                             variable=var, orient=tk.HORIZONTAL, length=200,
                             command=lambda v: self.on_parameter_change(param_name, float(v)))
            slider.grid(row=row, column=1, sticky=(tk.W, tk.E), padx=(0, 10))

            # 数值显示
            value_label = ttk.Label(self.scrollable_frame, text=f"{current_value:.2f}")
            value_label.grid(row=row, column=2, sticky=tk.W)

            # 更新显示的回调
            def update_display(value):
                value_label.config(text=f"{float(value):.2f}")
                self.on_parameter_change(param_name, float(value))

            slider.config(command=update_display)
            widget = slider

        else:
            # 字符串参数 - 使用输入框
            var = tk.StringVar(value=str(current_value))
            widget = ttk.Entry(self.scrollable_frame, textvariable=var, width=20)
            widget.grid(row=row, column=1, sticky=(tk.W, tk.E))
            widget.bind('<Return>', lambda e: self.on_parameter_change(param_name, var.get()))
            widget.bind('<FocusOut>', lambda e: self.on_parameter_change(param_name, var.get()))

        self.param_vars[param_name] = var
        self.param_widgets[param_name] = widget

    def on_parameter_change(self, param_name, value):
        """参数变化回调"""
        def update_thread():
            success = self.parent.set_filter_parameter(self.source_name, self.filter_name, param_name, value)
            if success:
                self.window.after(0, lambda: self.parent.log(f"🎛️ {param_name} = {value}"))

        threading.Thread(target=update_thread, daemon=True).start()

    def reset_to_defaults(self):
        """重置为默认值"""
        preset_params = self.get_preset_parameters()

        for param_name, param_info in preset_params.items():
            default_value = param_info.get('default', 0)
            if param_name in self.param_vars:
                self.param_vars[param_name].set(default_value)
                self.on_parameter_change(param_name, default_value)


class ChunkAnalyzerWindow:
    """VST Chunk数据分析窗口"""

    def __init__(self, parent, source_name, filter_info):
        self.parent = parent
        self.source_name = source_name
        self.filter_info = filter_info
        self.filter_name = filter_info['name']

        # 创建窗口
        self.window = tk.Toplevel(parent.root)
        self.window.title(f"VST Chunk数据分析 - {self.filter_name}")
        self.window.geometry("700x500")
        self.window.resizable(True, True)

        self.setup_ui()
        self.load_chunk_data()

    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 滤镜信息
        info_frame = ttk.LabelFrame(main_frame, text="滤镜信息", padding="5")
        info_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(info_frame, text=f"媒体源: {self.source_name}").grid(row=0, column=0, sticky=tk.W)
        ttk.Label(info_frame, text=f"滤镜名称: {self.filter_name}").grid(row=1, column=0, sticky=tk.W)
        ttk.Label(info_frame, text=f"滤镜类型: {self.filter_info['kind']}").grid(row=2, column=0, sticky=tk.W)

        # Chunk数据显示
        chunk_frame = ttk.LabelFrame(main_frame, text="Chunk数据", padding="5")
        chunk_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 原始数据
        ttk.Label(chunk_frame, text="原始Base64数据:").grid(row=0, column=0, sticky=tk.W)
        self.raw_text = tk.Text(chunk_frame, height=4, wrap=tk.WORD)
        self.raw_text.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        raw_scrollbar = ttk.Scrollbar(chunk_frame, orient=tk.VERTICAL, command=self.raw_text.yview)
        raw_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.raw_text.configure(yscrollcommand=raw_scrollbar.set)

        # 解析数据
        ttk.Label(chunk_frame, text="解析结果:").grid(row=2, column=0, sticky=tk.W)
        self.parsed_text = tk.Text(chunk_frame, height=15, wrap=tk.WORD)
        self.parsed_text.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        parsed_scrollbar = ttk.Scrollbar(chunk_frame, orient=tk.VERTICAL, command=self.parsed_text.yview)
        parsed_scrollbar.grid(row=3, column=1, sticky=(tk.N, tk.S))
        self.parsed_text.configure(yscrollcommand=parsed_scrollbar.set)

        # 按钮区域
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=2, column=0, sticky=(tk.W, tk.E))

        ttk.Button(btn_frame, text="刷新数据", command=self.load_chunk_data).grid(row=0, column=0, sticky=tk.W)
        ttk.Button(btn_frame, text="测试Chunk", command=self.test_chunk_input).grid(row=0, column=1, padx=(10, 0), sticky=tk.W)
        ttk.Button(btn_frame, text="关闭", command=self.window.destroy).grid(row=0, column=2, padx=(10, 0), sticky=tk.E)

        # 配置网格权重
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        chunk_frame.columnconfigure(0, weight=1)
        chunk_frame.rowconfigure(3, weight=1)
        btn_frame.columnconfigure(2, weight=1)

    def load_chunk_data(self):
        """加载chunk数据"""
        def load_thread():
            settings = self.parent.get_filter_settings(self.source_name, self.filter_name)
            chunk_data = settings.get('chunk_data', '')
            self.window.after(0, lambda: self.display_chunk_data(chunk_data))

        threading.Thread(target=load_thread, daemon=True).start()

    def display_chunk_data(self, chunk_data):
        """显示chunk数据"""
        # 清空文本框
        self.raw_text.delete(1.0, tk.END)
        self.parsed_text.delete(1.0, tk.END)

        if not chunk_data:
            self.parsed_text.insert(tk.END, "❌ 未找到chunk数据\n")
            self.parsed_text.insert(tk.END, "可能的原因:\n")
            self.parsed_text.insert(tk.END, "1. VST插件未完全加载\n")
            self.parsed_text.insert(tk.END, "2. 插件不支持chunk数据\n")
            self.parsed_text.insert(tk.END, "3. 需要手动打开插件界面进行初始化\n")
            return

        # 显示原始数据
        self.raw_text.insert(tk.END, chunk_data)

        # 解析chunk数据
        try:
            parsed_info = self.parse_chunk_data(chunk_data)
            self.parsed_text.insert(tk.END, parsed_info)
        except Exception as e:
            self.parsed_text.insert(tk.END, f"❌ 解析失败: {e}\n")

    def parse_chunk_data(self, chunk_data):
        """解析chunk数据"""
        try:
            # 解码base64数据
            binary_data = base64.b64decode(chunk_data)

            result = []
            result.append("📊 Chunk数据分析结果\n")
            result.append("=" * 50 + "\n\n")

            result.append(f"📏 数据长度: {len(binary_data)} 字节\n")
            result.append(f"📝 Base64长度: {len(chunk_data)} 字符\n\n")

            # 尝试解析为浮点数数组
            result.append("🔢 浮点数参数解析:\n")
            result.append("-" * 30 + "\n")

            float_count = len(binary_data) // 4
            for i in range(min(float_count, 50)):  # 最多显示50个参数
                try:
                    offset = i * 4
                    if offset + 4 <= len(binary_data):
                        value = struct.unpack('<f', binary_data[offset:offset+4])[0]
                        result.append(f"参数 {i:2d}: {value:12.6f}")

                        # 添加可能的含义
                        if abs(value) < 0.001:
                            result.append(" (可能是0)")
                        elif abs(value - 1.0) < 0.001:
                            result.append(" (可能是100%)")
                        elif abs(value - 0.5) < 0.001:
                            result.append(" (可能是50%)")
                        elif -12 <= value <= 12 and abs(value - round(value)) < 0.1:
                            result.append(f" (可能是音调: {round(value)}半音)")
                        elif 0 <= value <= 1:
                            result.append(f" (可能是比例: {value*100:.1f}%)")

                        result.append("\n")
                except:
                    result.append(f"参数 {i:2d}: 解析失败\n")

            if float_count > 50:
                result.append(f"... 还有 {float_count - 50} 个参数\n")

            result.append("\n")

            # 十六进制显示（前128字节）
            result.append("🔍 十六进制数据 (前128字节):\n")
            result.append("-" * 30 + "\n")

            hex_data = binary_data[:128]
            for i in range(0, len(hex_data), 16):
                line = hex_data[i:i+16]
                hex_str = ' '.join(f'{b:02X}' for b in line)
                ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in line)
                result.append(f"{i:04X}: {hex_str:<48} {ascii_str}\n")

            if len(binary_data) > 128:
                result.append(f"... 还有 {len(binary_data) - 128} 字节\n")

            return ''.join(result)

        except Exception as e:
            return f"❌ 解析chunk数据失败: {e}\n"

    def test_chunk_input(self):
        """测试输入的chunk数据"""
        # 创建输入对话框
        test_window = tk.Toplevel(self.window)
        test_window.title("测试Chunk数据")
        test_window.geometry("600x400")

        frame = ttk.Frame(test_window, padding="10")
        frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        ttk.Label(frame, text="输入Base64编码的Chunk数据:").grid(row=0, column=0, sticky=tk.W)

        input_text = tk.Text(frame, height=8, wrap=tk.WORD)
        input_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(5, 10))

        # 预填充你提供的数据
        sample_data = "CyC6kgEAAAAAAAAAAAEDAAAAAABBAAAA7zs1PwAAAAAAAAAAAAAAAAAAAAAAAIA/AAAAAAAAAADvOzU/AAAAAAAAAAAAAAAAAACAPwAAAD8AAAAAAAAAPwAAgD8AAIA/FaTSPgAAgD/TMgE/zcxMPgAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAAD8AAAA/AAAAPwAAAD8AAAA/AAAAPwAAAD8AAAA/AAAAPwAAAD8AAAA/AAAAPwAAAAAAAAAAAAAAAAAAAAAAAIA/AAAAPwAAAD8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD8AAAAAAACAPwAAgD8AAIA/AAAAAAAAAAAAAAAA"
        input_text.insert(tk.END, sample_data)

        result_text = tk.Text(frame, height=8, wrap=tk.WORD)
        result_text.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        def analyze_input():
            chunk_data = input_text.get(1.0, tk.END).strip()
            if chunk_data:
                try:
                    parsed_info = self.parse_chunk_data(chunk_data)
                    result_text.delete(1.0, tk.END)
                    result_text.insert(tk.END, parsed_info)
                except Exception as e:
                    result_text.delete(1.0, tk.END)
                    result_text.insert(tk.END, f"❌ 分析失败: {e}")

        btn_frame = ttk.Frame(frame)
        btn_frame.grid(row=3, column=0, sticky=(tk.W, tk.E))

        ttk.Button(btn_frame, text="分析数据", command=analyze_input).grid(row=0, column=0, sticky=tk.W)
        ttk.Button(btn_frame, text="关闭", command=test_window.destroy).grid(row=0, column=1, padx=(10, 0), sticky=tk.E)

        # 配置网格权重
        test_window.columnconfigure(0, weight=1)
        test_window.rowconfigure(0, weight=1)
        frame.columnconfigure(0, weight=1)
        frame.rowconfigure(1, weight=1)
        frame.rowconfigure(2, weight=1)
        btn_frame.columnconfigure(1, weight=1)

        # 自动分析预填充的数据
        analyze_input()


if __name__ == "__main__":
    app = OBSVSTManager()
    app.run()
