#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OBS VST滤镜管理器
功能：连接OBS、获取媒体源、管理滤镜、添加VST滤镜
"""

import tkinter as tk
from tkinter import ttk, messagebox
import websocket
import json
import time
import threading
import os

class OBSVSTManager:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("OBS VST滤镜管理器")
        self.root.geometry("800x600")
        
        # WebSocket连接
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0
        
        # 数据存储
        self.media_sources = []
        self.current_filters = []
        self.selected_source = ""
        
        # VST插件配置
        self.vst_plugins = {
            "Auburn Sounds Graillon 3-64": {
                "display_name": "Graillon 音调变声器",
                "filter_kind": "vst_filter",
                "plugin_path": r"C:\Program Files\VstPlugins\Auburn Sounds\Graillon 3-64.dll"
            },
            "TSE_808_2.0_x64": {
                "display_name": "TSE808 失真效果器", 
                "filter_kind": "vst_filter",
                "plugin_path": r"C:\Program Files\VstPlugins\TSE_808_2.0_x64.dll"
            },
            "TAL-Reverb-4-64": {
                "display_name": "TAL 混响效果器",
                "filter_kind": "vst_filter", 
                "plugin_path": r"C:\Program Files\VstPlugins\TAL-Reverb-4-64.dll"
            }
        }
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 连接区域
        conn_frame = ttk.LabelFrame(main_frame, text="OBS连接", padding="5")
        conn_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(conn_frame, text="WebSocket地址:").grid(row=0, column=0, sticky=tk.W)
        self.ws_url_var = tk.StringVar(value="ws://localhost:4455")
        ttk.Entry(conn_frame, textvariable=self.ws_url_var, width=30).grid(row=0, column=1, padx=(5, 10))
        
        self.connect_btn = ttk.Button(conn_frame, text="连接OBS", command=self.connect_obs)
        self.connect_btn.grid(row=0, column=2)
        
        self.status_label = ttk.Label(conn_frame, text="未连接", foreground="red")
        self.status_label.grid(row=0, column=3, padx=(10, 0))
        
        # 媒体源区域
        source_frame = ttk.LabelFrame(main_frame, text="媒体源管理", padding="5")
        source_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(source_frame, text="选择媒体源:").grid(row=0, column=0, sticky=tk.W)
        self.source_combo = ttk.Combobox(source_frame, state="readonly", width=40)
        self.source_combo.grid(row=0, column=1, padx=(5, 10))
        self.source_combo.bind('<<ComboboxSelected>>', self.on_source_selected)
        
        ttk.Button(source_frame, text="刷新媒体源", command=self.refresh_media_sources).grid(row=0, column=2)
        
        # 滤镜管理区域
        filter_frame = ttk.LabelFrame(main_frame, text="滤镜管理", padding="5")
        filter_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 滤镜列表
        columns = ('name', 'type', 'enabled')
        self.filter_tree = ttk.Treeview(filter_frame, columns=columns, show='headings', height=8)
        self.filter_tree.heading('name', text='滤镜名称')
        self.filter_tree.heading('type', text='滤镜类型')
        self.filter_tree.heading('enabled', text='状态')
        
        self.filter_tree.column('name', width=200)
        self.filter_tree.column('type', width=150)
        self.filter_tree.column('enabled', width=80)
        
        self.filter_tree.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 滚动条
        scrollbar = ttk.Scrollbar(filter_frame, orient=tk.VERTICAL, command=self.filter_tree.yview)
        scrollbar.grid(row=0, column=3, sticky=(tk.N, tk.S))
        self.filter_tree.configure(yscrollcommand=scrollbar.set)
        
        ttk.Button(filter_frame, text="刷新滤镜", command=self.refresh_filters).grid(row=1, column=0, sticky=tk.W)
        
        # VST插件添加区域
        vst_frame = ttk.LabelFrame(main_frame, text="添加VST滤镜", padding="5")
        vst_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(vst_frame, text="选择VST插件:").grid(row=0, column=0, sticky=tk.W)
        self.vst_combo = ttk.Combobox(vst_frame, values=list(self.vst_plugins.keys()), 
                                     state="readonly", width=30)
        self.vst_combo.grid(row=0, column=1, padx=(5, 10))
        
        ttk.Label(vst_frame, text="滤镜名称:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.filter_name_var = tk.StringVar(value="VST滤镜")
        ttk.Entry(vst_frame, textvariable=self.filter_name_var, width=20).grid(row=0, column=3, padx=(5, 10))
        
        ttk.Button(vst_frame, text="添加VST滤镜", command=self.add_vst_filter).grid(row=0, column=4)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="5")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        self.log_text = tk.Text(log_frame, height=8, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        main_frame.rowconfigure(4, weight=1)
        filter_frame.columnconfigure(0, weight=1)
        filter_frame.rowconfigure(0, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
    def log(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        print(message)  # 同时输出到控制台
        
    def connect_obs(self):
        """连接到OBS"""
        if self.is_connected:
            self.disconnect_obs()
            return
            
        self.log("🔄 正在连接到OBS...")
        self.connect_btn.config(state="disabled")
        
        def connect_thread():
            try:
                obs_ws_url = self.ws_url_var.get()
                self.ws = websocket.create_connection(obs_ws_url, timeout=5)
                self.log("✅ WebSocket连接已建立，等待Hello消息...")
                
                # 接收Hello消息 (Opcode 0)
                hello_raw = self.ws.recv()
                hello_data = json.loads(hello_raw)
                self.log(f"📨 收到Hello消息: OBS WebSocket v{hello_data.get('d', {}).get('obsWebSocketVersion', 'Unknown')}")
                
                if hello_data.get("op") != 0:
                    raise ValueError("收到的第一个消息不是Hello (Opcode 0)")
                
                rpc_version = hello_data.get("d", {}).get("rpcVersion", 1)
                authentication_required = hello_data.get("d", {}).get("authentication") is not None
                
                if authentication_required:
                    raise ConnectionAbortedError("OBS需要身份验证，请在OBS中禁用身份验证")
                
                # 发送Identify消息 (Opcode 1)
                identify_payload = {
                    "op": 1,
                    "d": {
                        "rpcVersion": rpc_version,
                        "eventSubscriptions": 0  # 不订阅事件
                    }
                }
                self.ws.send(json.dumps(identify_payload))
                self.log("📤 已发送Identify消息...")
                
                # 接收Identified消息 (Opcode 2)
                identified_raw = self.ws.recv()
                identified_data = json.loads(identified_raw)
                self.log(f"📨 收到Identified消息: {identified_data}")
                
                if identified_data.get("op") != 2:
                    raise ValueError("收到的第二个消息不是Identified (Opcode 2)")
                
                # 连接成功
                self.root.after(0, self.on_connect_success)
                
            except Exception as e:
                self.root.after(0, self.on_connect_error, str(e))
        
        threading.Thread(target=connect_thread, daemon=True).start()
        
    def on_connect_success(self):
        """连接成功回调"""
        self.is_connected = True
        self.status_label.config(text="已连接", foreground="green")
        self.connect_btn.config(text="断开连接", state="normal")
        self.log("🎉 OBS连接成功！")
        
        # 自动刷新媒体源
        self.refresh_media_sources()
        
    def on_connect_error(self, error_msg):
        """连接失败回调"""
        self.is_connected = False
        self.status_label.config(text="连接失败", foreground="red")
        self.connect_btn.config(text="连接OBS", state="normal")
        self.log(f"❌ 连接失败: {error_msg}")
        
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
    def disconnect_obs(self):
        """断开OBS连接"""
        self.is_connected = False
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
        self.status_label.config(text="未连接", foreground="red")
        self.connect_btn.config(text="连接OBS")
        self.log("👋 已断开OBS连接")
        
    def send_request(self, request_type, request_data=None, timeout=10):
        """发送请求并获取响应"""
        if not self.is_connected or not self.ws:
            return None
            
        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"
        
        payload = {
            "op": 6,  # Request
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            self.ws.send(json.dumps(payload))
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)
                    
                    if (response_data.get("op") == 7 and 
                        response_data.get("d", {}).get("requestId") == request_id):
                        return response_data.get("d", {})
                        
                except websocket.WebSocketTimeoutException:
                    continue
                except:
                    continue
                    
            return None
            
        except Exception as e:
            self.log(f"❌ 发送请求失败: {e}")
            return None

    def refresh_media_sources(self):
        """刷新媒体源列表"""
        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接到OBS")
            return

        self.log("🔍 正在获取媒体源列表...")

        def refresh_thread():
            response_data = self.send_request("GetInputList")
            if not response_data:
                self.root.after(0, lambda: self.log("❌ 获取媒体源失败"))
                return

            request_status = response_data.get("requestStatus", {})
            if not request_status.get("result"):
                error_msg = request_status.get("comment", "未知错误")
                self.root.after(0, lambda: self.log(f"❌ 获取媒体源失败: {error_msg}"))
                return

            inputs_data = response_data.get("responseData", {})
            inputs_list = inputs_data.get("inputs", [])

            # 处理媒体源数据
            self.root.after(0, lambda: self.process_media_sources(inputs_list))

        threading.Thread(target=refresh_thread, daemon=True).start()

    def process_media_sources(self, inputs_list):
        """处理媒体源数据"""
        self.media_sources = []
        source_names = []

        self.log(f"✅ 成功获取到 {len(inputs_list)} 个输入源")

        for input_item in inputs_list:
            input_name = input_item.get('inputName', '')
            input_kind = input_item.get('inputKind', '')

            # 存储媒体源信息
            source_info = {
                'name': input_name,
                'kind': input_kind,
                'unversioned_kind': input_item.get('unversionedInputKind', ''),
            }
            self.media_sources.append(source_info)
            source_names.append(input_name)

            # 分类显示
            if input_kind in ['ffmpeg_source', 'vlc_source']:
                self.log(f"  🎬 媒体源: {input_name} ({input_kind})")
            elif input_kind in ['wasapi_input_capture', 'wasapi_output_capture', 'pulse_input_capture']:
                self.log(f"  🎵 音频源: {input_name} ({input_kind})")
            elif input_kind in ['dshow_input', 'v4l2_input']:
                self.log(f"  📹 视频源: {input_name} ({input_kind})")
            else:
                self.log(f"  ⚪ 其他源: {input_name} ({input_kind})")

        # 更新下拉框
        self.source_combo['values'] = source_names
        if source_names:
            self.source_combo.set(source_names[0])
            self.selected_source = source_names[0]
            self.refresh_filters()

        self.log(f"📊 共找到 {len(source_names)} 个媒体源")

    def on_source_selected(self, event):
        """媒体源选择事件"""
        self.selected_source = self.source_combo.get()
        self.log(f"📌 选择媒体源: {self.selected_source}")
        self.refresh_filters()

    def refresh_filters(self):
        """刷新滤镜列表"""
        if not self.selected_source:
            return

        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接到OBS")
            return

        self.log(f"🔍 正在获取媒体源 '{self.selected_source}' 的滤镜...")

        def refresh_thread():
            response_data = self.send_request("GetSourceFilterList", {"sourceName": self.selected_source})
            if not response_data:
                self.root.after(0, lambda: self.log("❌ 获取滤镜失败"))
                return

            request_status = response_data.get("requestStatus", {})
            if not request_status.get("result"):
                error_msg = request_status.get("comment", "未知错误")
                self.root.after(0, lambda: self.log(f"❌ 获取滤镜失败: {error_msg}"))
                return

            filters_data = response_data.get("responseData", {})
            filters_list = filters_data.get("filters", [])

            # 处理滤镜数据
            self.root.after(0, lambda: self.process_filters(filters_list))

        threading.Thread(target=refresh_thread, daemon=True).start()

    def process_filters(self, filters_list):
        """处理滤镜数据"""
        # 清空树形控件
        for item in self.filter_tree.get_children():
            self.filter_tree.delete(item)

        self.current_filters = []
        vst_count = 0

        self.log(f"✅ 找到 {len(filters_list)} 个滤镜")

        for filter_item in filters_list:
            filter_name = filter_item.get('filterName', '')
            filter_kind = filter_item.get('filterKind', '')
            filter_enabled = filter_item.get('filterEnabled', False)

            # 存储滤镜数据
            filter_info = {
                'name': filter_name,
                'kind': filter_kind,
                'enabled': filter_enabled,
                'settings': filter_item.get('filterSettings', {})
            }
            self.current_filters.append(filter_info)

            # 检查是否为VST滤镜
            is_vst = 'vst' in filter_kind.lower()
            if is_vst:
                vst_count += 1

            # 添加到树形控件
            enabled_text = "✅ 启用" if filter_enabled else "❌ 禁用"
            item_id = self.filter_tree.insert('', tk.END, values=(filter_name, filter_kind, enabled_text))

            # 为VST滤镜添加特殊标记
            if is_vst:
                self.filter_tree.set(item_id, 'vst_filter', 'true')
                self.log(f"  🎛️ VST滤镜: {filter_name} ({filter_kind})")
            else:
                self.log(f"  ⚪ 其他滤镜: {filter_name} ({filter_kind})")

        if vst_count > 0:
            self.log(f"🎛️ 共找到 {vst_count} 个VST滤镜")
        else:
            self.log("📭 未找到VST滤镜")

    def add_vst_filter(self):
        """添加VST滤镜"""
        if not self.selected_source:
            messagebox.showwarning("警告", "请先选择媒体源")
            return

        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接到OBS")
            return

        plugin_name = self.vst_combo.get()
        filter_name = self.filter_name_var.get().strip()

        if not plugin_name:
            messagebox.showwarning("警告", "请选择VST插件")
            return

        if not filter_name:
            messagebox.showwarning("警告", "请输入滤镜名称")
            return

        plugin_config = self.vst_plugins[plugin_name]
        plugin_path = plugin_config["plugin_path"]

        # 检查插件文件是否存在
        if not os.path.exists(plugin_path):
            messagebox.showerror("错误", f"VST插件文件不存在:\n{plugin_path}")
            return

        self.log(f"🔧 正在添加VST滤镜: {filter_name} ({plugin_config['display_name']})")

        def add_thread():
            try:
                # 检查滤镜是否已存在
                existing_response = self.send_request("GetSourceFilter", {
                    "sourceName": self.selected_source,
                    "filterName": filter_name
                })

                if existing_response and existing_response.get("requestStatus", {}).get("result"):
                    self.root.after(0, lambda: self.log(f"⚠️ 滤镜 '{filter_name}' 已存在，将先删除"))

                    delete_response = self.send_request("RemoveSourceFilter", {
                        "sourceName": self.selected_source,
                        "filterName": filter_name
                    })
                    time.sleep(0.5)

                # 创建VST滤镜
                filter_settings = {
                    "plugin_path": plugin_path
                }

                response = self.send_request("CreateSourceFilter", {
                    "sourceName": self.selected_source,
                    "filterName": filter_name,
                    "filterKind": plugin_config["filter_kind"],
                    "filterSettings": filter_settings
                })

                if response and response.get("requestStatus", {}).get("result"):
                    self.root.after(0, lambda: [
                        self.log(f"✅ VST滤镜 '{filter_name}' 添加成功"),
                        self.refresh_filters()
                    ])
                else:
                    error_msg = response.get("requestStatus", {}).get("comment", "未知错误") if response else "无响应"
                    self.root.after(0, lambda: self.log(f"❌ 创建VST滤镜失败: {error_msg}"))

            except Exception as e:
                self.root.after(0, lambda: self.log(f"❌ 添加VST滤镜失败: {e}"))

        threading.Thread(target=add_thread, daemon=True).start()

    def run(self):
        """运行程序"""
        self.log("🎛️ OBS VST滤镜管理器已启动")
        self.log("请先连接到OBS，然后选择媒体源")

        try:
            self.root.mainloop()
        finally:
            if self.is_connected:
                self.disconnect_obs()

if __name__ == "__main__":
    app = OBSVSTManager()
    app.run()
