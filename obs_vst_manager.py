#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OBS VST滤镜管理器
功能：连接OBS、获取媒体源、管理滤镜、添加VST滤镜
"""

import tkinter as tk
from tkinter import ttk, messagebox
import websocket
import json
import time
import threading
import os
import base64
import struct

class OBSVSTManager:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("OBS VST滤镜管理器")
        self.root.geometry("800x600")
        
        # WebSocket连接
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0
        
        # 数据存储
        self.media_sources = []
        self.current_filters = []
        self.selected_source = ""
        
        # VST插件配置 - 根据你的实际路径更新
        self.vst_plugins = {
            "Auburn Sounds Graillon 3-64": {
                "display_name": "Graillon 音调变声器",
                "filter_kind": "vst_filter",
                "plugin_path": r"C:\Program Files\VSTPlugins\Auburn Sounds Graillon 3-64.dll"
            },
            "TSE_808_2.0_x64": {
                "display_name": "TSE808 失真效果器",
                "filter_kind": "vst_filter",
                "plugin_path": r"C:\Program Files\VSTPlugins\TSE_808_2.0_x64.dll"
            },
            "TAL-Reverb-4-64": {
                "display_name": "TAL 混响效果器",
                "filter_kind": "vst_filter",
                "plugin_path": r"C:\Program Files\VSTPlugins\TAL-Reverb-4-64.dll"
            }
        }
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 连接区域
        conn_frame = ttk.LabelFrame(main_frame, text="OBS连接", padding="5")
        conn_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(conn_frame, text="WebSocket地址:").grid(row=0, column=0, sticky=tk.W)
        self.ws_url_var = tk.StringVar(value="ws://localhost:4455")
        ttk.Entry(conn_frame, textvariable=self.ws_url_var, width=30).grid(row=0, column=1, padx=(5, 10))
        
        self.connect_btn = ttk.Button(conn_frame, text="连接OBS", command=self.connect_obs)
        self.connect_btn.grid(row=0, column=2)
        
        self.status_label = ttk.Label(conn_frame, text="未连接", foreground="red")
        self.status_label.grid(row=0, column=3, padx=(10, 0))
        
        # 媒体源区域
        source_frame = ttk.LabelFrame(main_frame, text="媒体源管理", padding="5")
        source_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(source_frame, text="选择媒体源:").grid(row=0, column=0, sticky=tk.W)
        self.source_combo = ttk.Combobox(source_frame, state="readonly", width=40)
        self.source_combo.grid(row=0, column=1, padx=(5, 10))
        self.source_combo.bind('<<ComboboxSelected>>', self.on_source_selected)
        
        ttk.Button(source_frame, text="刷新媒体源", command=self.refresh_media_sources).grid(row=0, column=2)
        
        # 滤镜管理区域
        filter_frame = ttk.LabelFrame(main_frame, text="滤镜管理", padding="5")
        filter_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 滤镜列表
        columns = ('name', 'type', 'enabled')
        self.filter_tree = ttk.Treeview(filter_frame, columns=columns, show='headings', height=8)
        self.filter_tree.heading('name', text='滤镜名称')
        self.filter_tree.heading('type', text='滤镜类型')
        self.filter_tree.heading('enabled', text='状态')
        
        self.filter_tree.column('name', width=200)
        self.filter_tree.column('type', width=150)
        self.filter_tree.column('enabled', width=80)
        
        self.filter_tree.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 滚动条
        scrollbar = ttk.Scrollbar(filter_frame, orient=tk.VERTICAL, command=self.filter_tree.yview)
        scrollbar.grid(row=0, column=3, sticky=(tk.N, tk.S))
        self.filter_tree.configure(yscrollcommand=scrollbar.set)
        
        filter_btn_frame = ttk.Frame(filter_frame)
        filter_btn_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(5, 0))

        ttk.Button(filter_btn_frame, text="刷新滤镜", command=self.refresh_filters).grid(row=0, column=0, sticky=tk.W)
        ttk.Button(filter_btn_frame, text="重载插件", command=self.reload_vst_plugin).grid(row=0, column=1, padx=(10, 0), sticky=tk.W)
        ttk.Button(filter_btn_frame, text="调试参数", command=self.open_debug_window).grid(row=0, column=2, padx=(10, 0), sticky=tk.W)
        ttk.Button(filter_btn_frame, text="快速调节", command=self.open_quick_parameter_window).grid(row=0, column=3, padx=(10, 0), sticky=tk.W)
        ttk.Button(filter_btn_frame, text="高级参数", command=self.open_parameter_window).grid(row=0, column=4, padx=(10, 0), sticky=tk.W)
        ttk.Button(filter_btn_frame, text="分析Chunk", command=self.analyze_chunk_data).grid(row=0, column=5, padx=(10, 0), sticky=tk.W)
        ttk.Button(filter_btn_frame, text="删除滤镜", command=self.remove_selected_filter).grid(row=0, column=6, padx=(10, 0), sticky=tk.W)
        
        # VST插件添加区域
        vst_frame = ttk.LabelFrame(main_frame, text="添加VST滤镜", padding="5")
        vst_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(vst_frame, text="选择VST插件:").grid(row=0, column=0, sticky=tk.W)
        self.vst_combo = ttk.Combobox(vst_frame, values=list(self.vst_plugins.keys()), 
                                     state="readonly", width=30)
        self.vst_combo.grid(row=0, column=1, padx=(5, 10))
        
        ttk.Label(vst_frame, text="滤镜名称:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.filter_name_var = tk.StringVar(value="VST滤镜")
        ttk.Entry(vst_frame, textvariable=self.filter_name_var, width=20).grid(row=0, column=3, padx=(5, 10))
        
        ttk.Button(vst_frame, text="添加VST滤镜", command=self.add_vst_filter).grid(row=0, column=4)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="5")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        self.log_text = tk.Text(log_frame, height=8, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        main_frame.rowconfigure(4, weight=1)
        filter_frame.columnconfigure(0, weight=1)
        filter_frame.rowconfigure(0, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
    def log(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        print(message)  # 同时输出到控制台
        
    def connect_obs(self):
        """连接到OBS"""
        if self.is_connected:
            self.disconnect_obs()
            return
            
        self.log("🔄 正在连接到OBS...")
        self.connect_btn.config(state="disabled")
        
        def connect_thread():
            try:
                obs_ws_url = self.ws_url_var.get()
                self.ws = websocket.create_connection(obs_ws_url, timeout=5)
                self.log("✅ WebSocket连接已建立，等待Hello消息...")
                
                # 接收Hello消息 (Opcode 0)
                hello_raw = self.ws.recv()
                hello_data = json.loads(hello_raw)
                self.log(f"📨 收到Hello消息: OBS WebSocket v{hello_data.get('d', {}).get('obsWebSocketVersion', 'Unknown')}")
                
                if hello_data.get("op") != 0:
                    raise ValueError("收到的第一个消息不是Hello (Opcode 0)")
                
                rpc_version = hello_data.get("d", {}).get("rpcVersion", 1)
                authentication_required = hello_data.get("d", {}).get("authentication") is not None
                
                if authentication_required:
                    raise ConnectionAbortedError("OBS需要身份验证，请在OBS中禁用身份验证")
                
                # 发送Identify消息 (Opcode 1)
                identify_payload = {
                    "op": 1,
                    "d": {
                        "rpcVersion": rpc_version,
                        "eventSubscriptions": 0  # 不订阅事件
                    }
                }
                self.ws.send(json.dumps(identify_payload))
                self.log("📤 已发送Identify消息...")
                
                # 接收Identified消息 (Opcode 2)
                identified_raw = self.ws.recv()
                identified_data = json.loads(identified_raw)
                self.log(f"📨 收到Identified消息: {identified_data}")
                
                if identified_data.get("op") != 2:
                    raise ValueError("收到的第二个消息不是Identified (Opcode 2)")
                
                # 连接成功
                self.root.after(0, self.on_connect_success)
                
            except Exception as e:
                self.root.after(0, self.on_connect_error, str(e))
        
        threading.Thread(target=connect_thread, daemon=True).start()
        
    def on_connect_success(self):
        """连接成功回调"""
        self.is_connected = True
        self.status_label.config(text="已连接", foreground="green")
        self.connect_btn.config(text="断开连接", state="normal")
        self.log("🎉 OBS连接成功！")
        
        # 自动刷新媒体源
        self.refresh_media_sources()
        
    def on_connect_error(self, error_msg):
        """连接失败回调"""
        self.is_connected = False
        self.status_label.config(text="连接失败", foreground="red")
        self.connect_btn.config(text="连接OBS", state="normal")
        self.log(f"❌ 连接失败: {error_msg}")
        
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
    def disconnect_obs(self):
        """断开OBS连接"""
        self.is_connected = False
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
        self.status_label.config(text="未连接", foreground="red")
        self.connect_btn.config(text="连接OBS")
        self.log("👋 已断开OBS连接")
        
    def send_request(self, request_type, request_data=None, timeout=10):
        """发送请求并获取响应"""
        if not self.is_connected or not self.ws:
            return None
            
        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"
        
        payload = {
            "op": 6,  # Request
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            self.ws.send(json.dumps(payload))
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)
                    
                    if (response_data.get("op") == 7 and 
                        response_data.get("d", {}).get("requestId") == request_id):
                        return response_data.get("d", {})
                        
                except websocket.WebSocketTimeoutException:
                    continue
                except:
                    continue
                    
            return None
            
        except Exception as e:
            self.log(f"❌ 发送请求失败: {e}")
            return None

    def refresh_media_sources(self):
        """刷新媒体源列表"""
        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接到OBS")
            return

        self.log("🔍 正在获取媒体源列表...")

        def refresh_thread():
            response_data = self.send_request("GetInputList")
            if not response_data:
                self.root.after(0, lambda: self.log("❌ 获取媒体源失败"))
                return

            request_status = response_data.get("requestStatus", {})
            if not request_status.get("result"):
                error_msg = request_status.get("comment", "未知错误")
                self.root.after(0, lambda: self.log(f"❌ 获取媒体源失败: {error_msg}"))
                return

            inputs_data = response_data.get("responseData", {})
            inputs_list = inputs_data.get("inputs", [])

            # 处理媒体源数据
            self.root.after(0, lambda: self.process_media_sources(inputs_list))

        threading.Thread(target=refresh_thread, daemon=True).start()

    def process_media_sources(self, inputs_list):
        """处理媒体源数据"""
        self.media_sources = []
        source_names = []

        self.log(f"✅ 成功获取到 {len(inputs_list)} 个输入源")

        for input_item in inputs_list:
            input_name = input_item.get('inputName', '')
            input_kind = input_item.get('inputKind', '')

            # 存储媒体源信息
            source_info = {
                'name': input_name,
                'kind': input_kind,
                'unversioned_kind': input_item.get('unversionedInputKind', ''),
            }
            self.media_sources.append(source_info)
            source_names.append(input_name)

            # 分类显示
            if input_kind in ['ffmpeg_source', 'vlc_source']:
                self.log(f"  🎬 媒体源: {input_name} ({input_kind})")
            elif input_kind in ['wasapi_input_capture', 'wasapi_output_capture', 'pulse_input_capture']:
                self.log(f"  🎵 音频源: {input_name} ({input_kind})")
            elif input_kind in ['dshow_input', 'v4l2_input']:
                self.log(f"  📹 视频源: {input_name} ({input_kind})")
            else:
                self.log(f"  ⚪ 其他源: {input_name} ({input_kind})")

        # 更新下拉框
        self.source_combo['values'] = source_names
        if source_names:
            self.source_combo.set(source_names[0])
            self.selected_source = source_names[0]
            self.refresh_filters()

        self.log(f"📊 共找到 {len(source_names)} 个媒体源")

    def on_source_selected(self, event):
        """媒体源选择事件"""
        self.selected_source = self.source_combo.get()
        self.log(f"📌 选择媒体源: {self.selected_source}")
        self.refresh_filters()

    def refresh_filters(self):
        """刷新滤镜列表"""
        if not self.selected_source:
            return

        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接到OBS")
            return

        self.log(f"🔍 正在获取媒体源 '{self.selected_source}' 的滤镜...")

        def refresh_thread():
            response_data = self.send_request("GetSourceFilterList", {"sourceName": self.selected_source})
            if not response_data:
                self.root.after(0, lambda: self.log("❌ 获取滤镜失败"))
                return

            request_status = response_data.get("requestStatus", {})
            if not request_status.get("result"):
                error_msg = request_status.get("comment", "未知错误")
                self.root.after(0, lambda: self.log(f"❌ 获取滤镜失败: {error_msg}"))
                return

            filters_data = response_data.get("responseData", {})
            filters_list = filters_data.get("filters", [])

            # 处理滤镜数据
            self.root.after(0, lambda: self.process_filters(filters_list))

        threading.Thread(target=refresh_thread, daemon=True).start()

    def process_filters(self, filters_list):
        """处理滤镜数据"""
        # 清空树形控件
        for item in self.filter_tree.get_children():
            self.filter_tree.delete(item)

        self.current_filters = []
        vst_count = 0

        self.log(f"✅ 找到 {len(filters_list)} 个滤镜")

        for filter_item in filters_list:
            filter_name = filter_item.get('filterName', '')
            filter_kind = filter_item.get('filterKind', '')
            filter_enabled = filter_item.get('filterEnabled', False)

            # 存储滤镜数据
            filter_info = {
                'name': filter_name,
                'kind': filter_kind,
                'enabled': filter_enabled,
                'settings': filter_item.get('filterSettings', {})
            }
            self.current_filters.append(filter_info)

            # 检查是否为VST滤镜
            is_vst = 'vst' in filter_kind.lower()
            if is_vst:
                vst_count += 1

            # 添加到树形控件
            enabled_text = "✅ 启用" if filter_enabled else "❌ 禁用"
            item_id = self.filter_tree.insert('', tk.END, values=(filter_name, filter_kind, enabled_text))

            # 显示滤镜信息（移除无效的列设置）
            if is_vst:
                self.log(f"  🎛️ VST滤镜: {filter_name} ({filter_kind})")
            else:
                self.log(f"  ⚪ 其他滤镜: {filter_name} ({filter_kind})")

        if vst_count > 0:
            self.log(f"🎛️ 共找到 {vst_count} 个VST滤镜")
        else:
            self.log("📭 未找到VST滤镜")

    def add_vst_filter(self):
        """添加VST滤镜"""
        if not self.selected_source:
            messagebox.showwarning("警告", "请先选择媒体源")
            return

        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接到OBS")
            return

        plugin_name = self.vst_combo.get()
        filter_name = self.filter_name_var.get().strip()

        if not plugin_name:
            messagebox.showwarning("警告", "请选择VST插件")
            return

        if not filter_name:
            messagebox.showwarning("警告", "请输入滤镜名称")
            return

        plugin_config = self.vst_plugins[plugin_name]
        plugin_path = plugin_config["plugin_path"]

        # 检查插件文件是否存在
        if not os.path.exists(plugin_path):
            messagebox.showerror("错误", f"VST插件文件不存在:\n{plugin_path}")
            return

        self.log(f"🔧 正在添加VST滤镜: {filter_name} ({plugin_config['display_name']})")

        def add_thread():
            try:
                # 检查滤镜是否已存在
                existing_response = self.send_request("GetSourceFilter", {
                    "sourceName": self.selected_source,
                    "filterName": filter_name
                })

                if existing_response and existing_response.get("requestStatus", {}).get("result"):
                    self.root.after(0, lambda: self.log(f"⚠️ 滤镜 '{filter_name}' 已存在，将先删除"))

                    delete_response = self.send_request("RemoveSourceFilter", {
                        "sourceName": self.selected_source,
                        "filterName": filter_name
                    })
                    time.sleep(0.5)

                # 创建VST滤镜
                filter_settings = {
                    "plugin_path": plugin_path
                }

                response = self.send_request("CreateSourceFilter", {
                    "sourceName": self.selected_source,
                    "filterName": filter_name,
                    "filterKind": plugin_config["filter_kind"],
                    "filterSettings": filter_settings
                })

                if response and response.get("requestStatus", {}).get("result"):
                    self.root.after(0, lambda: [
                        self.log(f"✅ VST滤镜 '{filter_name}' 添加成功"),
                        self.refresh_filters()
                    ])
                else:
                    error_msg = response.get("requestStatus", {}).get("comment", "未知错误") if response else "无响应"
                    self.root.after(0, lambda: self.log(f"❌ 创建VST滤镜失败: {error_msg}"))

            except Exception as e:
                self.root.after(0, lambda: self.log(f"❌ 添加VST滤镜失败: {e}"))

        threading.Thread(target=add_thread, daemon=True).start()

    def get_selected_filter(self):
        """获取选中的滤镜"""
        selection = self.filter_tree.selection()
        if not selection:
            return None

        item = selection[0]
        filter_name = self.filter_tree.item(item)['values'][0]

        # 从当前滤镜列表中找到对应的滤镜信息
        for filter_info in self.current_filters:
            if filter_info['name'] == filter_name:
                return filter_info
        return None

    def remove_selected_filter(self):
        """删除选中的滤镜"""
        filter_info = self.get_selected_filter()
        if not filter_info:
            messagebox.showwarning("警告", "请先选择要删除的滤镜")
            return

        if not self.selected_source:
            messagebox.showwarning("警告", "请先选择媒体源")
            return

        filter_name = filter_info['name']

        # 确认删除
        if not messagebox.askyesno("确认删除", f"确定要删除滤镜 '{filter_name}' 吗？"):
            return

        self.log(f"🗑️ 正在删除滤镜: {filter_name}")

        def remove_thread():
            try:
                response = self.send_request("RemoveSourceFilter", {
                    "sourceName": self.selected_source,
                    "filterName": filter_name
                })

                if response and response.get("requestStatus", {}).get("result"):
                    self.root.after(0, lambda: [
                        self.log(f"✅ 滤镜 '{filter_name}' 删除成功"),
                        self.refresh_filters()
                    ])
                else:
                    error_msg = response.get("requestStatus", {}).get("comment", "未知错误") if response else "无响应"
                    self.root.after(0, lambda: self.log(f"❌ 删除滤镜失败: {error_msg}"))

            except Exception as e:
                self.root.after(0, lambda: self.log(f"❌ 删除滤镜失败: {e}"))

        threading.Thread(target=remove_thread, daemon=True).start()

    def reload_vst_plugin(self):
        """重新加载VST插件"""
        filter_info = self.get_selected_filter()
        if not filter_info:
            messagebox.showwarning("警告", "请先选择要重载的滤镜")
            return

        if not self.selected_source:
            messagebox.showwarning("警告", "请先选择媒体源")
            return

        # 检查是否为VST滤镜
        if 'vst' not in filter_info['kind'].lower():
            messagebox.showinfo("提示", "只能重载VST滤镜")
            return

        filter_name = filter_info['name']

        # 确认重载
        if not messagebox.askyesno("确认重载", f"确定要重新加载VST滤镜 '{filter_name}' 吗？\n\n这将：\n1. 保存当前插件路径\n2. 删除现有滤镜\n3. 重新创建滤镜\n4. 重新加载插件"):
            return

        self.log(f"🔄 正在重新加载VST滤镜: {filter_name}")

        def reload_thread():
            try:
                # 1. 获取当前滤镜设置
                current_settings = self.parent.get_filter_settings(self.selected_source, filter_name) if hasattr(self, 'parent') else self.get_filter_settings(self.selected_source, filter_name)
                plugin_path = current_settings.get('plugin_path', '')

                if not plugin_path:
                    self.root.after(0, lambda: self.log("❌ 无法获取插件路径，重载失败"))
                    return

                self.root.after(0, lambda: self.log(f"📁 插件路径: {plugin_path}"))

                # 2. 删除现有滤镜
                self.root.after(0, lambda: self.log("🗑️ 删除现有滤镜..."))
                delete_response = self.send_request("RemoveSourceFilter", {
                    "sourceName": self.selected_source,
                    "filterName": filter_name
                })

                if not delete_response or not delete_response.get("requestStatus", {}).get("result"):
                    self.root.after(0, lambda: self.log("❌ 删除滤镜失败"))
                    return

                # 等待删除完成
                time.sleep(1)

                # 3. 重新创建滤镜
                self.root.after(0, lambda: self.log("🔧 重新创建滤镜..."))

                # 确定滤镜类型
                filter_kind = "vst_filter"  # 默认类型

                create_response = self.send_request("CreateSourceFilter", {
                    "sourceName": self.selected_source,
                    "filterName": filter_name,
                    "filterKind": filter_kind,
                    "filterSettings": {
                        "plugin_path": plugin_path
                    }
                })

                if create_response and create_response.get("requestStatus", {}).get("result"):
                    # 等待插件加载
                    time.sleep(2)

                    self.root.after(0, lambda: [
                        self.log(f"✅ VST滤镜 '{filter_name}' 重载成功"),
                        self.log("💡 插件已重新加载，参数已重置为默认值"),
                        self.refresh_filters()
                    ])
                else:
                    error_msg = create_response.get("requestStatus", {}).get("comment", "未知错误") if create_response else "无响应"
                    self.root.after(0, lambda: self.log(f"❌ 重新创建滤镜失败: {error_msg}"))

            except Exception as e:
                self.root.after(0, lambda: self.log(f"❌ 重载VST滤镜失败: {e}"))

        threading.Thread(target=reload_thread, daemon=True).start()

    def open_parameter_window(self):
        """打开参数调节窗口"""
        filter_info = self.get_selected_filter()
        if not filter_info:
            messagebox.showwarning("警告", "请先选择要调节的滤镜")
            return

        if not self.selected_source:
            messagebox.showwarning("警告", "请先选择媒体源")
            return

        # 检查是否为VST滤镜
        if 'vst' not in filter_info['kind'].lower():
            messagebox.showinfo("提示", "只能调节VST滤镜的参数")
            return

        # 创建参数调节窗口
        param_window = VSTParameterWindow(self, self.selected_source, filter_info)

    def open_quick_parameter_window(self):
        """打开快速参数调节窗口"""
        filter_info = self.get_selected_filter()
        if not filter_info:
            messagebox.showwarning("警告", "请先选择要调节的滤镜")
            return

        if not self.selected_source:
            messagebox.showwarning("警告", "请先选择媒体源")
            return

        # 检查是否为VST滤镜
        if 'vst' not in filter_info['kind'].lower():
            messagebox.showinfo("提示", "只能调节VST滤镜的参数")
            return

        # 检查插件状态，如果需要则提示重载
        if not self.auto_reload_if_needed(self.selected_source, filter_info['name']):
            # 创建快速参数调节窗口
            quick_window = QuickParameterWindow(self, self.selected_source, filter_info)

    def check_vst_plugin_status(self, source_name, filter_name):
        """检查VST插件状态"""
        try:
            settings = self.get_filter_settings(source_name, filter_name)

            # 检查是否有chunk数据（表示插件已加载）
            has_chunk = 'chunk_data' in settings and settings['chunk_data']

            # 检查是否有插件路径
            has_path = 'plugin_path' in settings and settings['plugin_path']

            # 检查参数数量（加载的插件通常有更多参数）
            param_count = len(settings)

            status = {
                'has_chunk': has_chunk,
                'has_path': has_path,
                'param_count': param_count,
                'is_loaded': has_chunk and param_count > 2,  # 简单的加载判断
                'settings': settings
            }

            return status

        except Exception as e:
            self.log(f"❌ 检查插件状态失败: {e}")
            return {'is_loaded': False, 'error': str(e)}

    def auto_reload_if_needed(self, source_name, filter_name):
        """如果需要则自动重载插件"""
        status = self.check_vst_plugin_status(source_name, filter_name)

        if not status.get('is_loaded', False):
            self.log(f"⚠️ 检测到VST插件 '{filter_name}' 可能未完全加载")
            self.log(f"   - 有Chunk数据: {status.get('has_chunk', False)}")
            self.log(f"   - 有插件路径: {status.get('has_path', False)}")
            self.log(f"   - 参数数量: {status.get('param_count', 0)}")

            # 询问是否自动重载
            if messagebox.askyesno("插件状态检测",
                                 f"检测到VST插件 '{filter_name}' 可能未完全加载。\n\n"
                                 f"状态信息:\n"
                                 f"• Chunk数据: {'有' if status.get('has_chunk') else '无'}\n"
                                 f"• 插件路径: {'有' if status.get('has_path') else '无'}\n"
                                 f"• 参数数量: {status.get('param_count', 0)}\n\n"
                                 f"是否自动重新加载插件？"):

                # 模拟选择该滤镜并重载
                for item in self.filter_tree.get_children():
                    if self.filter_tree.item(item)['values'][0] == filter_name:
                        self.filter_tree.selection_set(item)
                        break

                self.reload_vst_plugin()
                return True

        return False

    def set_filter_parameter(self, source_name, filter_name, param_name, value):
        """设置滤镜参数"""
        try:
            # 先获取当前设置
            current_settings = self.get_filter_settings(source_name, filter_name)
            self.log(f"🔍 当前滤镜设置: {list(current_settings.keys())}")

            # 尝试多种参数设置方式
            success = False

            # 方式1: 直接设置参数
            self.log(f"🔧 尝试方式1: 直接设置 {param_name} = {value}")
            response1 = self.send_request("SetSourceFilterSettings", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterSettings": {param_name: value}
            })

            if response1 and response1.get("requestStatus", {}).get("result"):
                self.log(f"✅ 方式1成功: {param_name} = {value}")
                success = True
            else:
                error_msg = response1.get("requestStatus", {}).get("comment", "未知错误") if response1 else "无响应"
                self.log(f"❌ 方式1失败: {error_msg}")

                # 方式2: 尝试VST特定的参数名称
                vst_param_names = [
                    f"vst_{param_name}",
                    f"param_{param_name}",
                    f"{param_name}_value",
                    param_name.upper(),
                    param_name.lower()
                ]

                for vst_param in vst_param_names:
                    self.log(f"🔧 尝试方式2: VST参数名 {vst_param} = {value}")
                    response2 = self.send_request("SetSourceFilterSettings", {
                        "sourceName": source_name,
                        "filterName": filter_name,
                        "filterSettings": {vst_param: value}
                    })

                    if response2 and response2.get("requestStatus", {}).get("result"):
                        self.log(f"✅ 方式2成功: {vst_param} = {value}")
                        success = True
                        break
                    else:
                        error_msg = response2.get("requestStatus", {}).get("comment", "未知错误") if response2 else "无响应"
                        self.log(f"❌ 方式2失败 ({vst_param}): {error_msg}")

                # 方式3: 尝试通过chunk数据设置
                if not success and 'chunk_data' in current_settings:
                    self.log(f"🔧 尝试方式3: 通过chunk数据设置")
                    # 这里可以尝试修改chunk数据的方式
                    # 暂时跳过，因为比较复杂

            # 验证设置是否生效
            if success:
                # 等待一下让设置生效
                time.sleep(0.1)
                new_settings = self.get_filter_settings(source_name, filter_name)
                self.log(f"🔍 设置后的滤镜设置: {list(new_settings.keys())}")

                # 检查参数是否真的改变了
                if param_name in new_settings:
                    actual_value = new_settings[param_name]
                    if abs(float(actual_value) - float(value)) < 0.01:
                        self.log(f"✅ 参数验证成功: {param_name} = {actual_value}")
                    else:
                        self.log(f"⚠️ 参数值不匹配: 期望 {value}, 实际 {actual_value}")
                else:
                    self.log(f"⚠️ 参数 {param_name} 在设置中未找到")

            return success

        except Exception as e:
            self.log(f"❌ 参数设置失败: {e}")
            return False

    def get_filter_settings(self, source_name, filter_name):
        """获取滤镜设置"""
        try:
            response = self.send_request("GetSourceFilter", {
                "sourceName": source_name,
                "filterName": filter_name
            })

            if response and response.get("requestStatus", {}).get("result"):
                return response.get("responseData", {}).get("filterSettings", {})
            else:
                return {}

        except Exception as e:
            self.log(f"❌ 获取滤镜设置失败: {e}")
            return {}

    def analyze_chunk_data(self):
        """分析VST插件的chunk数据"""
        filter_info = self.get_selected_filter()
        if not filter_info:
            messagebox.showwarning("警告", "请先选择要分析的滤镜")
            return

        if not self.selected_source:
            messagebox.showwarning("警告", "请先选择媒体源")
            return

        # 检查是否为VST滤镜
        if 'vst' not in filter_info['kind'].lower():
            messagebox.showinfo("提示", "只能分析VST滤镜的chunk数据")
            return

        # 创建chunk分析窗口
        chunk_window = ChunkAnalyzerWindow(self, self.selected_source, filter_info)

    def open_debug_window(self):
        """打开VST参数调试窗口"""
        filter_info = self.get_selected_filter()
        if not filter_info:
            messagebox.showwarning("警告", "请先选择要调试的滤镜")
            return

        if not self.selected_source:
            messagebox.showwarning("警告", "请先选择媒体源")
            return

        # 检查是否为VST滤镜
        if 'vst' not in filter_info['kind'].lower():
            messagebox.showinfo("提示", "只能调试VST滤镜")
            return

        # 创建调试窗口
        debug_window = VSTDebugWindow(self, self.selected_source, filter_info)

    def run(self):
        """运行程序"""
        self.log("🎛️ OBS VST滤镜管理器已启动")
        self.log("请先连接到OBS，然后选择媒体源")

        try:
            self.root.mainloop()
        finally:
            if self.is_connected:
                self.disconnect_obs()


class VSTParameterWindow:
    """VST参数调节窗口"""

    def __init__(self, parent, source_name, filter_info):
        self.parent = parent
        self.source_name = source_name
        self.filter_info = filter_info
        self.filter_name = filter_info['name']

        # 创建窗口
        self.window = tk.Toplevel(parent.root)
        self.window.title(f"VST参数调节 - {self.filter_name}")
        self.window.geometry("500x400")
        self.window.resizable(True, True)

        # 参数控件存储
        self.param_vars = {}
        self.param_widgets = {}

        self.setup_ui()
        self.load_current_settings()

    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 滤镜信息
        info_frame = ttk.LabelFrame(main_frame, text="滤镜信息", padding="5")
        info_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(info_frame, text=f"媒体源: {self.source_name}").grid(row=0, column=0, sticky=tk.W)
        ttk.Label(info_frame, text=f"滤镜名称: {self.filter_name}").grid(row=1, column=0, sticky=tk.W)
        ttk.Label(info_frame, text=f"滤镜类型: {self.filter_info['kind']}").grid(row=2, column=0, sticky=tk.W)

        # 参数调节区域
        param_frame = ttk.LabelFrame(main_frame, text="参数调节", padding="5")
        param_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 创建滚动区域
        canvas = tk.Canvas(param_frame, height=200)
        scrollbar = ttk.Scrollbar(param_frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 按钮区域
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=2, column=0, sticky=(tk.W, tk.E))

        ttk.Button(btn_frame, text="刷新参数", command=self.load_current_settings).grid(row=0, column=0, sticky=tk.W)
        ttk.Button(btn_frame, text="重置默认", command=self.reset_to_defaults).grid(row=0, column=1, padx=(10, 0), sticky=tk.W)
        ttk.Button(btn_frame, text="关闭", command=self.window.destroy).grid(row=0, column=2, padx=(10, 0), sticky=tk.E)

        # 配置网格权重
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        param_frame.columnconfigure(0, weight=1)
        param_frame.rowconfigure(0, weight=1)
        btn_frame.columnconfigure(2, weight=1)

    def load_current_settings(self):
        """加载当前参数设置"""
        def load_thread():
            settings = self.parent.get_filter_settings(self.source_name, self.filter_name)
            self.window.after(0, lambda: self.display_parameters(settings))

        threading.Thread(target=load_thread, daemon=True).start()

    def display_parameters(self, settings):
        """显示参数控件"""
        # 清空现有控件
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        self.param_vars.clear()
        self.param_widgets.clear()

        if not settings:
            ttk.Label(self.scrollable_frame, text="无法获取参数信息").grid(row=0, column=0, pady=10)
            return

        # 根据滤镜类型创建预设参数
        preset_params = self.get_preset_parameters()

        row = 0

        # 显示预设参数
        if preset_params:
            ttk.Label(self.scrollable_frame, text="常用参数:", font=('Arial', 10, 'bold')).grid(row=row, column=0, columnspan=3, sticky=tk.W, pady=(0, 5))
            row += 1

            for param_name, param_info in preset_params.items():
                current_value = settings.get(param_name, param_info.get('default', 0))
                self.create_parameter_widget(row, param_name, current_value, param_info)
                row += 1

        # 显示其他参数
        other_params = {k: v for k, v in settings.items() if k not in preset_params}
        if other_params:
            ttk.Separator(self.scrollable_frame, orient='horizontal').grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
            row += 1

            ttk.Label(self.scrollable_frame, text="其他参数:", font=('Arial', 10, 'bold')).grid(row=row, column=0, columnspan=3, sticky=tk.W, pady=(0, 5))
            row += 1

            for param_name, value in other_params.items():
                param_info = {'min': -100, 'max': 100, 'default': 0, 'description': param_name}
                self.create_parameter_widget(row, param_name, value, param_info)
                row += 1

    def get_preset_parameters(self):
        """获取预设参数配置"""
        filter_kind = self.filter_info['kind'].lower()

        if 'graillon' in self.filter_name.lower() or 'pitch' in filter_kind:
            return {
                'pitch': {'min': -12, 'max': 12, 'default': 0, 'description': '音调偏移 (半音)'},
                'formant': {'min': 50, 'max': 150, 'default': 100, 'description': '共振峰调节 (%)'},
                'mix': {'min': 0, 'max': 100, 'default': 100, 'description': '干湿混合 (%)'},
            }
        elif 'tse' in self.filter_name.lower() or '808' in self.filter_name.lower():
            return {
                'drive': {'min': 0, 'max': 100, 'default': 30, 'description': '失真强度 (%)'},
                'tone': {'min': 0, 'max': 100, 'default': 50, 'description': '音色控制 (%)'},
                'level': {'min': 0, 'max': 100, 'default': 80, 'description': '输出电平 (%)'},
            }
        elif 'tal' in self.filter_name.lower() or 'reverb' in self.filter_name.lower():
            return {
                'roomsize': {'min': 0, 'max': 100, 'default': 40, 'description': '房间大小 (%)'},
                'damping': {'min': 0, 'max': 100, 'default': 60, 'description': '阻尼控制 (%)'},
                'mix': {'min': 0, 'max': 100, 'default': 25, 'description': '混响混合 (%)'},
            }
        else:
            return {}

    def create_parameter_widget(self, row, param_name, current_value, param_info):
        """创建参数控件"""
        # 参数名称和描述
        desc = param_info.get('description', param_name)
        ttk.Label(self.scrollable_frame, text=f"{desc}:").grid(row=row, column=0, sticky=tk.W, padx=(0, 10))

        # 判断参数类型
        if isinstance(current_value, bool):
            # 布尔参数 - 使用复选框
            var = tk.BooleanVar(value=current_value)
            widget = ttk.Checkbutton(self.scrollable_frame, variable=var,
                                   command=lambda: self.on_parameter_change(param_name, var.get()))
            widget.grid(row=row, column=1, sticky=tk.W)

        elif isinstance(current_value, (int, float)):
            # 数值参数 - 使用滑块和输入框
            var = tk.DoubleVar(value=float(current_value))

            # 滑块
            min_val = param_info.get('min', -100)
            max_val = param_info.get('max', 100)

            slider = ttk.Scale(self.scrollable_frame, from_=min_val, to=max_val,
                             variable=var, orient=tk.HORIZONTAL, length=200,
                             command=lambda v: self.on_parameter_change(param_name, float(v)))
            slider.grid(row=row, column=1, sticky=(tk.W, tk.E), padx=(0, 10))

            # 数值显示
            value_label = ttk.Label(self.scrollable_frame, text=f"{current_value:.2f}")
            value_label.grid(row=row, column=2, sticky=tk.W)

            # 更新显示的回调
            def update_display(value):
                value_label.config(text=f"{float(value):.2f}")
                self.on_parameter_change(param_name, float(value))

            slider.config(command=update_display)
            widget = slider

        else:
            # 字符串参数 - 使用输入框
            var = tk.StringVar(value=str(current_value))
            widget = ttk.Entry(self.scrollable_frame, textvariable=var, width=20)
            widget.grid(row=row, column=1, sticky=(tk.W, tk.E))
            widget.bind('<Return>', lambda e: self.on_parameter_change(param_name, var.get()))
            widget.bind('<FocusOut>', lambda e: self.on_parameter_change(param_name, var.get()))

        self.param_vars[param_name] = var
        self.param_widgets[param_name] = widget

    def on_parameter_change(self, param_name, value):
        """参数变化回调"""
        def update_thread():
            success = self.parent.set_filter_parameter(self.source_name, self.filter_name, param_name, value)
            if success:
                self.window.after(0, lambda: self.parent.log(f"🎛️ {param_name} = {value}"))

        threading.Thread(target=update_thread, daemon=True).start()

    def reset_to_defaults(self):
        """重置为默认值"""
        preset_params = self.get_preset_parameters()

        for param_name, param_info in preset_params.items():
            default_value = param_info.get('default', 0)
            if param_name in self.param_vars:
                self.param_vars[param_name].set(default_value)
                self.on_parameter_change(param_name, default_value)


class QuickParameterWindow:
    """快速参数调节窗口"""

    def __init__(self, parent, source_name, filter_info):
        self.parent = parent
        self.source_name = source_name
        self.filter_info = filter_info
        self.filter_name = filter_info['name']

        # 创建窗口
        self.window = tk.Toplevel(parent.root)
        self.window.title(f"快速参数调节 - {self.filter_name}")
        self.window.geometry("400x500")
        self.window.resizable(False, True)

        self.setup_ui()
        self.check_plugin_status()

    def check_plugin_status(self):
        """检查插件状态"""
        def check_thread():
            status = self.parent.check_vst_plugin_status(self.source_name, self.filter_name)
            if not status.get('is_loaded', False):
                self.window.after(0, lambda: self.show_status_warning(status))

        threading.Thread(target=check_thread, daemon=True).start()

    def show_status_warning(self, status):
        """显示状态警告"""
        # 在窗口顶部插入警告框
        warning_frame = ttk.LabelFrame(self.window, text="⚠️ 插件状态警告", padding="5")
        warning_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=10, pady=(5, 10))

        warning_text = f"检测到插件可能未完全加载：\n"
        warning_text += f"• Chunk数据: {'有' if status.get('has_chunk') else '无'}\n"
        warning_text += f"• 参数数量: {status.get('param_count', 0)}\n"
        warning_text += f"建议重新加载插件以获取完整功能"

        ttk.Label(warning_frame, text=warning_text, foreground="red").grid(row=0, column=0, sticky=tk.W)

        def reload_and_close():
            self.window.destroy()
            # 选择对应的滤镜
            for item in self.parent.filter_tree.get_children():
                if self.parent.filter_tree.item(item)['values'][0] == self.filter_name:
                    self.parent.filter_tree.selection_set(item)
                    break
            self.parent.reload_vst_plugin()

        ttk.Button(warning_frame, text="立即重载", command=reload_and_close).grid(row=0, column=1, padx=(10, 0))

        # 重新调整其他组件的行号
        for child in self.window.winfo_children():
            if child != warning_frame:
                info = child.grid_info()
                if info and 'row' in info:
                    child.grid(row=info['row'] + 1)

    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 滤镜信息
        info_frame = ttk.LabelFrame(main_frame, text="滤镜信息", padding="5")
        info_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(info_frame, text=f"媒体源: {self.source_name}").grid(row=0, column=0, sticky=tk.W)
        ttk.Label(info_frame, text=f"滤镜名称: {self.filter_name}").grid(row=1, column=0, sticky=tk.W)

        # 快速参数调节
        param_frame = ttk.LabelFrame(main_frame, text="快速参数调节", padding="5")
        param_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 根据滤镜类型显示对应的快速调节控件
        self.create_quick_controls(param_frame)

        # 预设按钮
        preset_frame = ttk.LabelFrame(main_frame, text="快速预设", padding="5")
        preset_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        self.create_preset_buttons(preset_frame)

        # 按钮区域
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=3, column=0, sticky=(tk.W, tk.E))

        ttk.Button(btn_frame, text="重置默认", command=self.reset_to_defaults).grid(row=0, column=0, sticky=tk.W)
        ttk.Button(btn_frame, text="关闭", command=self.window.destroy).grid(row=0, column=1, padx=(10, 0), sticky=tk.E)

        # 配置网格权重
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        btn_frame.columnconfigure(1, weight=1)

    def create_quick_controls(self, parent):
        """创建快速控制控件"""
        filter_name_lower = self.filter_name.lower()

        if 'graillon' in filter_name_lower or 'pitch' in filter_name_lower:
            self.create_graillon_controls(parent)
        elif 'tse' in filter_name_lower or '808' in filter_name_lower:
            self.create_tse808_controls(parent)
        elif 'tal' in filter_name_lower or 'reverb' in filter_name_lower:
            self.create_tal_reverb_controls(parent)
        else:
            self.create_generic_controls(parent)

    def create_graillon_controls(self, parent):
        """创建Graillon控件"""
        # 音调调节
        ttk.Label(parent, text="音调偏移 (半音):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.pitch_var = tk.DoubleVar(value=0.0)
        pitch_scale = ttk.Scale(parent, from_=-12, to=12, variable=self.pitch_var,
                               orient=tk.HORIZONTAL, length=250,
                               command=lambda v: self.set_parameter('pitch', float(v)))
        pitch_scale.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)

        self.pitch_label = ttk.Label(parent, text="0.0")
        self.pitch_label.grid(row=0, column=2, padx=(10, 0), pady=5)

        # 共振峰调节
        ttk.Label(parent, text="共振峰 (%):").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.formant_var = tk.DoubleVar(value=100.0)
        formant_scale = ttk.Scale(parent, from_=50, to=150, variable=self.formant_var,
                                 orient=tk.HORIZONTAL, length=250,
                                 command=lambda v: self.set_parameter('formant', float(v)))
        formant_scale.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)

        self.formant_label = ttk.Label(parent, text="100.0")
        self.formant_label.grid(row=1, column=2, padx=(10, 0), pady=5)

        # 混合比例
        ttk.Label(parent, text="干湿混合 (%):").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.mix_var = tk.DoubleVar(value=100.0)
        mix_scale = ttk.Scale(parent, from_=0, to=100, variable=self.mix_var,
                             orient=tk.HORIZONTAL, length=250,
                             command=lambda v: self.set_parameter('mix', float(v)))
        mix_scale.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)

        self.mix_label = ttk.Label(parent, text="100.0")
        self.mix_label.grid(row=2, column=2, padx=(10, 0), pady=5)

        # 更新显示的回调
        def update_pitch_display(value):
            self.pitch_label.config(text=f"{float(value):.1f}")
            self.set_parameter('pitch', float(value))

        def update_formant_display(value):
            self.formant_label.config(text=f"{float(value):.1f}")
            self.set_parameter('formant', float(value))

        def update_mix_display(value):
            self.mix_label.config(text=f"{float(value):.1f}")
            self.set_parameter('mix', float(value))

        pitch_scale.config(command=update_pitch_display)
        formant_scale.config(command=update_formant_display)
        mix_scale.config(command=update_mix_display)

    def create_tse808_controls(self, parent):
        """创建TSE808控件"""
        # 失真强度
        ttk.Label(parent, text="失真强度 (%):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.drive_var = tk.DoubleVar(value=30.0)
        drive_scale = ttk.Scale(parent, from_=0, to=100, variable=self.drive_var,
                               orient=tk.HORIZONTAL, length=250,
                               command=lambda v: self.set_parameter('drive', float(v)))
        drive_scale.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)

        self.drive_label = ttk.Label(parent, text="30.0")
        self.drive_label.grid(row=0, column=2, padx=(10, 0), pady=5)

        # 音色控制
        ttk.Label(parent, text="音色控制 (%):").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.tone_var = tk.DoubleVar(value=50.0)
        tone_scale = ttk.Scale(parent, from_=0, to=100, variable=self.tone_var,
                              orient=tk.HORIZONTAL, length=250,
                              command=lambda v: self.set_parameter('tone', float(v)))
        tone_scale.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)

        self.tone_label = ttk.Label(parent, text="50.0")
        self.tone_label.grid(row=1, column=2, padx=(10, 0), pady=5)

        # 输出电平
        ttk.Label(parent, text="输出电平 (%):").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.level_var = tk.DoubleVar(value=80.0)
        level_scale = ttk.Scale(parent, from_=0, to=100, variable=self.level_var,
                               orient=tk.HORIZONTAL, length=250,
                               command=lambda v: self.set_parameter('level', float(v)))
        level_scale.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)

        self.level_label = ttk.Label(parent, text="80.0")
        self.level_label.grid(row=2, column=2, padx=(10, 0), pady=5)

        # 更新显示的回调
        def update_drive_display(value):
            self.drive_label.config(text=f"{float(value):.1f}")
            self.set_parameter('drive', float(value))

        def update_tone_display(value):
            self.tone_label.config(text=f"{float(value):.1f}")
            self.set_parameter('tone', float(value))

        def update_level_display(value):
            self.level_label.config(text=f"{float(value):.1f}")
            self.set_parameter('level', float(value))

        drive_scale.config(command=update_drive_display)
        tone_scale.config(command=update_tone_display)
        level_scale.config(command=update_level_display)

    def create_tal_reverb_controls(self, parent):
        """创建TAL-Reverb控件"""
        # 房间大小
        ttk.Label(parent, text="房间大小 (%):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.roomsize_var = tk.DoubleVar(value=40.0)
        roomsize_scale = ttk.Scale(parent, from_=0, to=100, variable=self.roomsize_var,
                                  orient=tk.HORIZONTAL, length=250,
                                  command=lambda v: self.set_parameter('roomsize', float(v)))
        roomsize_scale.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)

        self.roomsize_label = ttk.Label(parent, text="40.0")
        self.roomsize_label.grid(row=0, column=2, padx=(10, 0), pady=5)

        # 阻尼控制
        ttk.Label(parent, text="阻尼控制 (%):").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.damping_var = tk.DoubleVar(value=60.0)
        damping_scale = ttk.Scale(parent, from_=0, to=100, variable=self.damping_var,
                                 orient=tk.HORIZONTAL, length=250,
                                 command=lambda v: self.set_parameter('damping', float(v)))
        damping_scale.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)

        self.damping_label = ttk.Label(parent, text="60.0")
        self.damping_label.grid(row=1, column=2, padx=(10, 0), pady=5)

        # 混响混合
        ttk.Label(parent, text="混响混合 (%):").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.reverb_mix_var = tk.DoubleVar(value=25.0)
        reverb_mix_scale = ttk.Scale(parent, from_=0, to=100, variable=self.reverb_mix_var,
                                    orient=tk.HORIZONTAL, length=250,
                                    command=lambda v: self.set_parameter('mix', float(v)))
        reverb_mix_scale.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)

        self.reverb_mix_label = ttk.Label(parent, text="25.0")
        self.reverb_mix_label.grid(row=2, column=2, padx=(10, 0), pady=5)

        # 更新显示的回调
        def update_roomsize_display(value):
            self.roomsize_label.config(text=f"{float(value):.1f}")
            self.set_parameter('roomsize', float(value))

        def update_damping_display(value):
            self.damping_label.config(text=f"{float(value):.1f}")
            self.set_parameter('damping', float(value))

        def update_reverb_mix_display(value):
            self.reverb_mix_label.config(text=f"{float(value):.1f}")
            self.set_parameter('mix', float(value))

        roomsize_scale.config(command=update_roomsize_display)
        damping_scale.config(command=update_damping_display)
        reverb_mix_scale.config(command=update_reverb_mix_display)

    def create_generic_controls(self, parent):
        """创建通用控件"""
        ttk.Label(parent, text="通用VST参数调节").grid(row=0, column=0, columnspan=3, pady=10)

        # 通用参数1
        ttk.Label(parent, text="参数1:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.param1_var = tk.DoubleVar(value=0.0)
        param1_scale = ttk.Scale(parent, from_=0, to=100, variable=self.param1_var,
                                orient=tk.HORIZONTAL, length=250,
                                command=lambda v: self.set_parameter('param1', float(v)))
        param1_scale.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)

        self.param1_label = ttk.Label(parent, text="0.0")
        self.param1_label.grid(row=1, column=2, padx=(10, 0), pady=5)

        def update_param1_display(value):
            self.param1_label.config(text=f"{float(value):.1f}")
            self.set_parameter('param1', float(value))

        param1_scale.config(command=update_param1_display)

    def create_preset_buttons(self, parent):
        """创建预设按钮"""
        filter_name_lower = self.filter_name.lower()

        if 'graillon' in filter_name_lower:
            # Graillon预设
            ttk.Button(parent, text="原声", command=lambda: self.apply_graillon_preset(0, 100, 100)).grid(row=0, column=0, padx=5, pady=2)
            ttk.Button(parent, text="轻微变声", command=lambda: self.apply_graillon_preset(2, 100, 80)).grid(row=0, column=1, padx=5, pady=2)
            ttk.Button(parent, text="女声效果", command=lambda: self.apply_graillon_preset(5, 120, 100)).grid(row=0, column=2, padx=5, pady=2)
            ttk.Button(parent, text="男声效果", command=lambda: self.apply_graillon_preset(-3, 80, 100)).grid(row=1, column=0, padx=5, pady=2)
            ttk.Button(parent, text="机器人声", command=lambda: self.apply_graillon_preset(-8, 60, 100)).grid(row=1, column=1, padx=5, pady=2)

        elif 'tse' in filter_name_lower or '808' in filter_name_lower:
            # TSE808预设
            ttk.Button(parent, text="无失真", command=lambda: self.apply_tse808_preset(0, 50, 80)).grid(row=0, column=0, padx=5, pady=2)
            ttk.Button(parent, text="轻微增色", command=lambda: self.apply_tse808_preset(20, 50, 80)).grid(row=0, column=1, padx=5, pady=2)
            ttk.Button(parent, text="温暖效果", command=lambda: self.apply_tse808_preset(40, 30, 75)).grid(row=0, column=2, padx=5, pady=2)
            ttk.Button(parent, text="明显失真", command=lambda: self.apply_tse808_preset(60, 60, 70)).grid(row=1, column=0, padx=5, pady=2)
            ttk.Button(parent, text="极限效果", command=lambda: self.apply_tse808_preset(80, 70, 65)).grid(row=1, column=1, padx=5, pady=2)

        elif 'tal' in filter_name_lower or 'reverb' in filter_name_lower:
            # TAL-Reverb预设
            ttk.Button(parent, text="无混响", command=lambda: self.apply_tal_reverb_preset(0, 0, 0)).grid(row=0, column=0, padx=5, pady=2)
            ttk.Button(parent, text="轻微空间", command=lambda: self.apply_tal_reverb_preset(30, 60, 15)).grid(row=0, column=1, padx=5, pady=2)
            ttk.Button(parent, text="自然房间", command=lambda: self.apply_tal_reverb_preset(50, 50, 25)).grid(row=0, column=2, padx=5, pady=2)
            ttk.Button(parent, text="大厅效果", command=lambda: self.apply_tal_reverb_preset(80, 40, 35)).grid(row=1, column=0, padx=5, pady=2)
            ttk.Button(parent, text="梦幻效果", command=lambda: self.apply_tal_reverb_preset(90, 20, 50)).grid(row=1, column=1, padx=5, pady=2)

    def apply_graillon_preset(self, pitch, formant, mix):
        """应用Graillon预设"""
        if hasattr(self, 'pitch_var'):
            self.pitch_var.set(pitch)
            self.pitch_label.config(text=f"{pitch:.1f}")
            self.set_parameter('pitch', pitch)

        if hasattr(self, 'formant_var'):
            self.formant_var.set(formant)
            self.formant_label.config(text=f"{formant:.1f}")
            self.set_parameter('formant', formant)

        if hasattr(self, 'mix_var'):
            self.mix_var.set(mix)
            self.mix_label.config(text=f"{mix:.1f}")
            self.set_parameter('mix', mix)

    def apply_tse808_preset(self, drive, tone, level):
        """应用TSE808预设"""
        if hasattr(self, 'drive_var'):
            self.drive_var.set(drive)
            self.drive_label.config(text=f"{drive:.1f}")
            self.set_parameter('drive', drive)

        if hasattr(self, 'tone_var'):
            self.tone_var.set(tone)
            self.tone_label.config(text=f"{tone:.1f}")
            self.set_parameter('tone', tone)

        if hasattr(self, 'level_var'):
            self.level_var.set(level)
            self.level_label.config(text=f"{level:.1f}")
            self.set_parameter('level', level)

    def apply_tal_reverb_preset(self, roomsize, damping, mix):
        """应用TAL-Reverb预设"""
        if hasattr(self, 'roomsize_var'):
            self.roomsize_var.set(roomsize)
            self.roomsize_label.config(text=f"{roomsize:.1f}")
            self.set_parameter('roomsize', roomsize)

        if hasattr(self, 'damping_var'):
            self.damping_var.set(damping)
            self.damping_label.config(text=f"{damping:.1f}")
            self.set_parameter('damping', damping)

        if hasattr(self, 'reverb_mix_var'):
            self.reverb_mix_var.set(mix)
            self.reverb_mix_label.config(text=f"{mix:.1f}")
            self.set_parameter('mix', mix)

    def set_parameter(self, param_name, value):
        """设置参数"""
        def update_thread():
            success = self.parent.set_filter_parameter(self.source_name, self.filter_name, param_name, value)
            if success:
                self.window.after(0, lambda: self.parent.log(f"🎛️ {param_name} = {value:.1f}"))

        threading.Thread(target=update_thread, daemon=True).start()

    def reset_to_defaults(self):
        """重置为默认值"""
        filter_name_lower = self.filter_name.lower()

        if 'graillon' in filter_name_lower:
            self.apply_graillon_preset(0, 100, 100)
        elif 'tse' in filter_name_lower or '808' in filter_name_lower:
            self.apply_tse808_preset(30, 50, 80)
        elif 'tal' in filter_name_lower or 'reverb' in filter_name_lower:
            self.apply_tal_reverb_preset(40, 60, 25)


class ChunkAnalyzerWindow:
    """VST Chunk数据分析窗口"""

    def __init__(self, parent, source_name, filter_info):
        self.parent = parent
        self.source_name = source_name
        self.filter_info = filter_info
        self.filter_name = filter_info['name']

        # 创建窗口
        self.window = tk.Toplevel(parent.root)
        self.window.title(f"VST Chunk数据分析 - {self.filter_name}")
        self.window.geometry("700x500")
        self.window.resizable(True, True)

        self.setup_ui()
        self.load_chunk_data()

    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 滤镜信息
        info_frame = ttk.LabelFrame(main_frame, text="滤镜信息", padding="5")
        info_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(info_frame, text=f"媒体源: {self.source_name}").grid(row=0, column=0, sticky=tk.W)
        ttk.Label(info_frame, text=f"滤镜名称: {self.filter_name}").grid(row=1, column=0, sticky=tk.W)
        ttk.Label(info_frame, text=f"滤镜类型: {self.filter_info['kind']}").grid(row=2, column=0, sticky=tk.W)

        # Chunk数据显示
        chunk_frame = ttk.LabelFrame(main_frame, text="Chunk数据", padding="5")
        chunk_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 原始数据
        ttk.Label(chunk_frame, text="原始Base64数据:").grid(row=0, column=0, sticky=tk.W)
        self.raw_text = tk.Text(chunk_frame, height=4, wrap=tk.WORD)
        self.raw_text.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        raw_scrollbar = ttk.Scrollbar(chunk_frame, orient=tk.VERTICAL, command=self.raw_text.yview)
        raw_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.raw_text.configure(yscrollcommand=raw_scrollbar.set)

        # 解析数据
        ttk.Label(chunk_frame, text="解析结果:").grid(row=2, column=0, sticky=tk.W)
        self.parsed_text = tk.Text(chunk_frame, height=15, wrap=tk.WORD)
        self.parsed_text.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        parsed_scrollbar = ttk.Scrollbar(chunk_frame, orient=tk.VERTICAL, command=self.parsed_text.yview)
        parsed_scrollbar.grid(row=3, column=1, sticky=(tk.N, tk.S))
        self.parsed_text.configure(yscrollcommand=parsed_scrollbar.set)

        # 按钮区域
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=2, column=0, sticky=(tk.W, tk.E))

        ttk.Button(btn_frame, text="刷新数据", command=self.load_chunk_data).grid(row=0, column=0, sticky=tk.W)
        ttk.Button(btn_frame, text="测试Chunk", command=self.test_chunk_input).grid(row=0, column=1, padx=(10, 0), sticky=tk.W)
        ttk.Button(btn_frame, text="关闭", command=self.window.destroy).grid(row=0, column=2, padx=(10, 0), sticky=tk.E)

        # 配置网格权重
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        chunk_frame.columnconfigure(0, weight=1)
        chunk_frame.rowconfigure(3, weight=1)
        btn_frame.columnconfigure(2, weight=1)

    def load_chunk_data(self):
        """加载chunk数据"""
        def load_thread():
            settings = self.parent.get_filter_settings(self.source_name, self.filter_name)
            chunk_data = settings.get('chunk_data', '')
            self.window.after(0, lambda: self.display_chunk_data(chunk_data))

        threading.Thread(target=load_thread, daemon=True).start()

    def display_chunk_data(self, chunk_data):
        """显示chunk数据"""
        # 清空文本框
        self.raw_text.delete(1.0, tk.END)
        self.parsed_text.delete(1.0, tk.END)

        if not chunk_data:
            self.parsed_text.insert(tk.END, "❌ 未找到chunk数据\n")
            self.parsed_text.insert(tk.END, "可能的原因:\n")
            self.parsed_text.insert(tk.END, "1. VST插件未完全加载\n")
            self.parsed_text.insert(tk.END, "2. 插件不支持chunk数据\n")
            self.parsed_text.insert(tk.END, "3. 需要手动打开插件界面进行初始化\n")
            return

        # 显示原始数据
        self.raw_text.insert(tk.END, chunk_data)

        # 解析chunk数据
        try:
            parsed_info = self.parse_chunk_data(chunk_data)
            self.parsed_text.insert(tk.END, parsed_info)
        except Exception as e:
            self.parsed_text.insert(tk.END, f"❌ 解析失败: {e}\n")

    def parse_chunk_data(self, chunk_data):
        """解析chunk数据"""
        try:
            # 解码base64数据
            binary_data = base64.b64decode(chunk_data)

            result = []
            result.append("📊 Chunk数据分析结果\n")
            result.append("=" * 50 + "\n\n")

            result.append(f"📏 数据长度: {len(binary_data)} 字节\n")
            result.append(f"📝 Base64长度: {len(chunk_data)} 字符\n\n")

            # 尝试解析为浮点数数组
            result.append("🔢 浮点数参数解析:\n")
            result.append("-" * 30 + "\n")

            float_count = len(binary_data) // 4
            for i in range(min(float_count, 50)):  # 最多显示50个参数
                try:
                    offset = i * 4
                    if offset + 4 <= len(binary_data):
                        value = struct.unpack('<f', binary_data[offset:offset+4])[0]
                        result.append(f"参数 {i:2d}: {value:12.6f}")

                        # 添加可能的含义
                        if abs(value) < 0.001:
                            result.append(" (可能是0)")
                        elif abs(value - 1.0) < 0.001:
                            result.append(" (可能是100%)")
                        elif abs(value - 0.5) < 0.001:
                            result.append(" (可能是50%)")
                        elif -12 <= value <= 12 and abs(value - round(value)) < 0.1:
                            result.append(f" (可能是音调: {round(value)}半音)")
                        elif 0 <= value <= 1:
                            result.append(f" (可能是比例: {value*100:.1f}%)")

                        result.append("\n")
                except:
                    result.append(f"参数 {i:2d}: 解析失败\n")

            if float_count > 50:
                result.append(f"... 还有 {float_count - 50} 个参数\n")

            result.append("\n")

            # 十六进制显示（前128字节）
            result.append("🔍 十六进制数据 (前128字节):\n")
            result.append("-" * 30 + "\n")

            hex_data = binary_data[:128]
            for i in range(0, len(hex_data), 16):
                line = hex_data[i:i+16]
                hex_str = ' '.join(f'{b:02X}' for b in line)
                ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in line)
                result.append(f"{i:04X}: {hex_str:<48} {ascii_str}\n")

            if len(binary_data) > 128:
                result.append(f"... 还有 {len(binary_data) - 128} 字节\n")

            return ''.join(result)

        except Exception as e:
            return f"❌ 解析chunk数据失败: {e}\n"

    def test_chunk_input(self):
        """测试输入的chunk数据"""
        # 创建输入对话框
        test_window = tk.Toplevel(self.window)
        test_window.title("测试Chunk数据")
        test_window.geometry("600x400")

        frame = ttk.Frame(test_window, padding="10")
        frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        ttk.Label(frame, text="输入Base64编码的Chunk数据:").grid(row=0, column=0, sticky=tk.W)

        input_text = tk.Text(frame, height=8, wrap=tk.WORD)
        input_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(5, 10))

        # 预填充你提供的数据
        sample_data = "CyC6kgEAAAAAAAAAAAEDAAAAAABBAAAA7zs1PwAAAAAAAAAAAAAAAAAAAAAAAIA/AAAAAAAAAADvOzU/AAAAAAAAAAAAAAAAAACAPwAAAD8AAAAAAAAAPwAAgD8AAIA/FaTSPgAAgD/TMgE/zcxMPgAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAAD8AAAA/AAAAPwAAAD8AAAA/AAAAPwAAAD8AAAA/AAAAPwAAAD8AAAA/AAAAPwAAAAAAAAAAAAAAAAAAAAAAAIA/AAAAPwAAAD8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD8AAAAAAACAPwAAgD8AAIA/AAAAAAAAAAAAAAAA"
        input_text.insert(tk.END, sample_data)

        result_text = tk.Text(frame, height=8, wrap=tk.WORD)
        result_text.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        def analyze_input():
            chunk_data = input_text.get(1.0, tk.END).strip()
            if chunk_data:
                try:
                    parsed_info = self.parse_chunk_data(chunk_data)
                    result_text.delete(1.0, tk.END)
                    result_text.insert(tk.END, parsed_info)
                except Exception as e:
                    result_text.delete(1.0, tk.END)
                    result_text.insert(tk.END, f"❌ 分析失败: {e}")

        btn_frame = ttk.Frame(frame)
        btn_frame.grid(row=3, column=0, sticky=(tk.W, tk.E))

        ttk.Button(btn_frame, text="分析数据", command=analyze_input).grid(row=0, column=0, sticky=tk.W)
        ttk.Button(btn_frame, text="关闭", command=test_window.destroy).grid(row=0, column=1, padx=(10, 0), sticky=tk.E)

        # 配置网格权重
        test_window.columnconfigure(0, weight=1)
        test_window.rowconfigure(0, weight=1)
        frame.columnconfigure(0, weight=1)
        frame.rowconfigure(1, weight=1)
        frame.rowconfigure(2, weight=1)
        btn_frame.columnconfigure(1, weight=1)

        # 自动分析预填充的数据
        analyze_input()


if __name__ == "__main__":
    app = OBSVSTManager()
    app.run()
