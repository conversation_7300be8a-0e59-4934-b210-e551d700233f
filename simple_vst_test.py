#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的VST控制器测试
验证基本功能是否正常
"""

from vst_complete_controller import VSTCompleteController

def main():
    print("🎛️ VST控制器简单测试")
    print("=" * 40)
    
    # 创建控制器
    controller = VSTCompleteController()
    
    # 连接到OBS
    print("1. 连接到OBS...")
    if not controller.connect():
        print("❌ 连接失败，请检查OBS是否启动并启用了WebSocket服务器")
        return
    
    try:
        # 获取音频源
        print("\n2. 获取音频源...")
        audio_sources = controller.get_audio_sources()
        
        if not audio_sources:
            print("❌ 未找到音频源")
            print("💡 请在OBS中添加音频源（如麦克风、媒体源等）")
        else:
            print(f"✅ 找到 {len(audio_sources)} 个音频源:")
            for i, source in enumerate(audio_sources, 1):
                print(f"   {i}. {source}")
        
        # 如果找到音频源，测试添加VST滤镜
        if audio_sources:
            test_source = audio_sources[0]
            print(f"\n3. 测试添加VST滤镜到: {test_source}")
            
            # 更新插件路径（请根据你的实际路径修改）
            print("💡 请确保VST插件路径正确")
            
            # 你可以在这里修改插件路径
            # controller.update_plugin_path("Graillon", r"你的Graillon插件路径")
            
            # 测试添加Graillon滤镜
            success = controller.add_vst_filter(test_source, "测试变声器", "Graillon")
            if success:
                print("✅ VST滤镜添加成功！")
                
                # 测试参数设置
                print("\n4. 测试参数设置...")
                controller.set_vst_parameter_by_name(test_source, "测试变声器", "pitch", 3.0)
                
            else:
                print("❌ VST滤镜添加失败")
                print("💡 可能的原因:")
                print("   - VST插件路径不正确")
                print("   - 插件文件不存在")
                print("   - 插件不兼容")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 断开连接
        controller.disconnect()
        print("\n👋 测试完成")

if __name__ == "__main__":
    main()
