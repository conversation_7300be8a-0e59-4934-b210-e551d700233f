#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试音频源获取功能
验证是否能正确获取OBS中的媒体源和其他音频源
"""

from vst_complete_controller import VSTCompleteController
import time

def test_audio_sources():
    """测试音频源获取"""
    print("🎵 测试音频源获取功能")
    print("=" * 50)
    
    # 创建控制器
    controller = VSTCompleteController()
    
    # 连接到OBS
    print("🔄 正在连接到OBS...")
    if not controller.connect():
        print("❌ 无法连接到OBS")
        print("请确保:")
        print("1. OBS已启动")
        print("2. WebSocket服务器已启用 (工具 → WebSocket服务器设置)")
        print("3. 端口设置为4455")
        print("4. 如果设置了密码，请在代码中配置")
        return
    
    try:
        # 获取音频源
        print("\n📋 获取音频源列表...")
        audio_sources = controller.get_audio_sources()
        
        if not audio_sources:
            print("❌ 未找到任何音频源")
            print("\n💡 可能的原因:")
            print("1. OBS中没有添加音频源")
            print("2. 音频源类型不被识别")
            print("3. 音频源没有音频轨道")
            
            # 尝试获取所有输入源进行调试
            print("\n🔍 调试信息 - 所有输入源:")
            try:
                response = controller.client.get_input_list()
                for i, input_item in enumerate(response.inputs, 1):
                    input_name = input_item.get('inputName', '')
                    input_kind = input_item.get('inputKind', '')
                    has_audio = input_item.get("audioTracks") is not None
                    audio_count = len(input_item.get("audioTracks", []))
                    
                    print(f"  {i:2d}. {input_name}")
                    print(f"      类型: {input_kind}")
                    print(f"      音频轨道: {'✅' if has_audio and audio_count > 0 else '❌'} ({audio_count})")
                    print()
            except Exception as e:
                print(f"❌ 获取调试信息失败: {e}")
        else:
            print(f"✅ 成功找到 {len(audio_sources)} 个音频源:")
            for i, source_name in enumerate(audio_sources, 1):
                print(f"  {i}. {source_name}")
                
                # 获取每个音频源的详细信息
                try:
                    # 获取输入设置
                    input_settings = controller.client.get_input_settings(input_name=source_name)
                    input_kind = input_settings.input_kind
                    
                    # 获取滤镜列表
                    filters = controller.client.get_source_filter_list(source_name=source_name)
                    filter_count = len(filters.filters)
                    
                    print(f"     类型: {input_kind}")
                    print(f"     滤镜数量: {filter_count}")
                    
                    # 如果有滤镜，显示VST滤镜
                    if filter_count > 0:
                        vst_filters = []
                        for filter_item in filters.filters:
                            filter_kind = filter_item.get('filterKind', '')
                            if 'vst' in filter_kind.lower():
                                vst_filters.append(filter_item.get('filterName', ''))
                        
                        if vst_filters:
                            print(f"     VST滤镜: {', '.join(vst_filters)}")
                        else:
                            print(f"     VST滤镜: 无")
                    
                    print()
                    
                except Exception as e:
                    print(f"     ⚠️ 获取详细信息失败: {e}")
                    print()
        
        # 如果找到了音频源，测试添加VST滤镜
        if audio_sources:
            print("\n🎛️ 测试VST滤镜功能...")
            test_source = audio_sources[0]
            print(f"选择测试源: {test_source}")
            
            # 检查是否已有VST滤镜
            existing_filters = controller.list_vst_filters(test_source)
            if existing_filters:
                print("现有VST滤镜:")
                for filter_info in existing_filters:
                    status = "✅ 启用" if filter_info['enabled'] else "❌ 禁用"
                    print(f"  🎛️ {filter_info['name']} - {status}")
            else:
                print("当前没有VST滤镜")
            
            # 询问是否要添加测试滤镜
            print("\n💡 提示: 你可以运行以下命令来添加VST滤镜:")
            print(f"python vst_controller_demo.py")
            print("或者使用GUI界面:")
            print(f"python vst_gui_controller.py")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 断开连接
        controller.disconnect()
        print("\n👋 测试完成")

def test_specific_source():
    """测试特定音频源"""
    print("🎯 测试特定音频源")
    print("=" * 50)
    
    # 让用户输入要测试的音频源名称
    source_name = input("请输入要测试的音频源名称 (如 '媒体源'): ").strip()
    
    if not source_name:
        print("❌ 未输入音频源名称")
        return
    
    controller = VSTCompleteController()
    
    if not controller.connect():
        print("❌ 无法连接到OBS")
        return
    
    try:
        print(f"\n🔍 检查音频源: {source_name}")
        
        # 检查源是否存在
        response = controller.client.get_input_list()
        source_exists = False
        source_info = None
        
        for input_item in response.inputs:
            if input_item.get('inputName') == source_name:
                source_exists = True
                source_info = input_item
                break
        
        if not source_exists:
            print(f"❌ 未找到音频源: {source_name}")
            print("\n📋 可用的输入源:")
            for input_item in response.inputs:
                print(f"  - {input_item.get('inputName', '')}")
            return
        
        print(f"✅ 找到音频源: {source_name}")
        print(f"   类型: {source_info.get('inputKind', '')}")
        print(f"   音频轨道: {len(source_info.get('audioTracks', []))}")
        
        # 获取滤镜列表
        filters = controller.list_vst_filters(source_name)
        print(f"   VST滤镜数量: {len(filters)}")
        
        for filter_info in filters:
            status = "✅ 启用" if filter_info['enabled'] else "❌ 禁用"
            print(f"     🎛️ {filter_info['name']} - {status}")
        
        # 测试获取参数
        if filters:
            test_filter = filters[0]['name']
            print(f"\n🔧 测试获取滤镜参数: {test_filter}")
            
            params = controller.get_vst_parameters(source_name, test_filter)
            if params:
                print("当前参数:")
                for param_name, value in params.items():
                    print(f"  {param_name}: {value}")
            else:
                print("未获取到参数")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        controller.disconnect()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "specific":
        test_specific_source()
    else:
        test_audio_sources()
        
        # 询问是否要测试特定源
        print("\n" + "=" * 50)
        choice = input("是否要测试特定音频源? (y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            test_specific_source()
